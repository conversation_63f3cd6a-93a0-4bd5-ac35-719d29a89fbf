import os
import subprocess
import traceback

import sys
import threading

import time

import psutil
from PyQt5.QtCore import QTimer, pyqtSignal
from PyQt5.QtWidgets import QWidget, QApplication, QMessageBox

from common.LogUtils import logger
from ui.ElevationAngle import Ui_elevationForm
from ui.canvas import CustomPlotWidget
from utils.elevation_angle_tool import elevation_angle_tool
from utils.witSensor import wit_serial_sensor

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)


class ElevationAngleView(QWidget, Ui_elevationForm):
    sensor_data = pyqtSignal(dict)
    def __init__(self, parent=None):
        super(ElevationAngleView, self).__init__(parent)
        self.setupUi(self)
        # self.elevation_angle_tool = elevation_angle_tool
        self.elevation_angle_tool = wit_serial_sensor
        self.setWindowTitle("仰角测试工具")

        # 初始化绘图组件
        # self.widget_display = CustomPlotWidget()
        # 如果你的UI中已经有一个占位符widget，可以用布局替换它
        # 或者直接添加到现有布局中

        # self.elevation_angle_tool.angle_changed.connect(self.set_angle)
        # self.elevation_angle_tool.sensor_data_changed.connect(self.set_angle)
        self.pushButtonStart.clicked.connect(self.start_elevation_angle)
        self.pushButtonStop.clicked.connect(self.stop_elevation_angle)
        self.timer = QTimer(self)
        self.timer.setInterval(100)  # 100毫秒=0.1秒
        self.timer.timeout.connect(self.get_current_sensor_data)
        self.sensor_data.connect(self.set_angle)

    def execute_comand(self, cmd):
        logger.info("execute_comand cmd={}".format(cmd))
        out = subprocess.Popen(cmd, shell=True, stderr=subprocess.STDOUT,
                               stdin=subprocess.PIPE,
                               bufsize=0,
                               stdout=subprocess.PIPE)
        output, error = out.communicate()

    def check_server(self):
        for proc in psutil.process_iter(['pid', 'name']):
            # 如果进程名匹配
            if proc.info['name'] == "orbbec_server.exe":
                break
        else:
            # 开启进程
            path = os.path.join(parent_dir, "orbbec", "orbbec_server.exe")
            threading.Thread(target=self.execute_comand, args=(path,)).start()
            logger.info("start orbbec_server...")
            time.sleep(.2)

    def start_elevation_angle(self):
        # 清空之前的数据并重置时间
        self.widget_display.clear_data()
        self.widget_display.reset_start_time()


        # 遍历所有的线程
        for thread in threading.enumerate():
            name = thread.name
            if thread.is_alive() and name == "read_serial_data":
                self.pushButtonStart.setEnabled(False)
                self.pushButtonStart.setText("正在检测")
                self.timer.start()
                break
        else:
            if self.elevation_angle_tool.is_connected:
                threading.Thread(target=self.elevation_angle_tool.start_monitoring,name="read_serial_data").start()
                self.pushButtonStart.setEnabled(False)
                self.pushButtonStart.setText("正在检测")
                self.timer.start()
            else:
                # 弹窗
                # QMessageBox.information("提示","检查传感器COM口是否连接")
                logger.info("start_elevation_angle 检查传感器COM口是否连接")
                # self.elevation_angle_tool.open("COM23")

    def get_current_sensor_data(self):
        try:

            sensor_data = self.elevation_angle_tool.current_sensor_data
            # print (sensor_data)
            if sensor_data["type_name"] == "角度":
                # print("qsize q", self.elevation_angle_tool.parsed_data_queue.qsize())
                self.sensor_data.emit(sensor_data)
            # self.elevation_angle_tool.parsed_data_queue.queue.clear()
        except Exception:
            print(traceback.format_exc())
            pass



    def stop_elevation_angle(self):
        # self.elevation_angle_tool.stop_detect_angle = True
        self.pushButtonStart.setEnabled(True)
        self.pushButtonStart.setText("测量")
        self.timer.stop()


    def set_angle(self, sensor_data):
        # 显示角度值到LCD
        if sensor_data["type_name"] == "角度" and self.elevation_angle_tool.running:
            value = sensor_data["value"]["X"]
            # formatted_number = "{:.1f}".format(value)
            angle = 180-abs(value)
            formatted_number =  "{:.1f}".format(angle)
            self.lcdNumber.display(formatted_number)

            # 添加数据点到图表
            if self.widget_display.start_time is None:
                self.widget_display.reset_start_time()

            current_time = time.time() - self.widget_display.start_time
            # print("add_data_point",value)
            self.widget_display.add_data_point(current_time, angle)


if __name__ == '__main__':
    app = QApplication(sys.argv)
    widget = ElevationAngleView()
    widget.show()
    sys.exit(app.exec_())
