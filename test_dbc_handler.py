#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DBC 消息处理器测试脚本
"""

import json
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 简化的日志类
class SimpleLogger:
    def info(self, msg): print(f"INFO: {msg}")
    def warning(self, msg): print(f"WARNING: {msg}")
    def error(self, msg): print(f"ERROR: {msg}")

# 创建简化版本的 DBC 处理器
from adb.DbcMessageHandler import DbcMessageHandler
dbc_handler = DbcMessageHandler()

# 测试数据
test_json = {
    "messages": {
        "Engine_Data": {
            "id": 256,
            "dlc": 8,
            "signals": [
                {
                    "name": "Engine_Speed",
                    "start_bit": 0,
                    "length": 16,
                    "factor": 0.25,
                    "offset": 0,
                    "current_value": 2500.75
                },
                {
                    "name": "Engine_Torque", 
                    "start_bit": 16,
                    "length": 16,
                    "factor": 0.1,
                    "offset": -500,
                    "current_value": 250.5
                },
                {
                    "name": "Throttle_Position",
                    "start_bit": 32,
                    "length": 8,
                    "factor": 0.4,
                    "offset": 0,
                    "current_value": 65.2
                }
            ]
        },
        "Vehicle_Speed": {
            "id": 512,
            "dlc": 8,
            "signals": [
                {
                    "name": "Vehicle_Speed",
                    "start_bit": 0,
                    "length": 16,
                    "factor": 0.0625,
                    "offset": 0,
                    "current_value": 80.5
                },
                {
                    "name": "Wheel_Speed_FL",
                    "start_bit": 16,
                    "length": 12,
                    "factor": 0.1,
                    "offset": 0,
                    "current_value": 81.2
                }
            ]
        },
        "Body_Control": {
            "id": 768,
            "dlc": 8,
            "signals": [
                {
                    "name": "Door_Status",
                    "start_bit": 0,
                    "length": 4,
                    "factor": 1,
                    "offset": 0,
                    "current_value": 5
                },
                {
                    "name": "Light_Status",
                    "start_bit": 8,
                    "length": 8,
                    "factor": 1,
                    "offset": 0,
                    "current_value": 12
                }
            ]
        }
    }
}

def test_dbc_parsing():
    """测试 DBC 解析功能"""
    print("=== DBC 消息解析测试 ===")
    
    # 1. 加载 JSON 数据
    print("\n1. 加载 DBC 消息定义...")
    success = dbc_handler.load_from_json(test_json)
    print(f"加载结果: {'成功' if success else '失败'}")
    
    if not success:
        return
    
    # 2. 显示加载的消息
    print(f"\n2. 加载的消息列表:")
    for msg_name in dbc_handler.get_all_message_names():
        message = dbc_handler.get_message_by_name(msg_name)
        print(f"   - {msg_name}: ID=0x{message.can_id:X}, DLC={message.dlc}, 信号数={len(message.signals)}")
    
    # 3. 测试消息编码
    print(f"\n3. 测试消息编码:")
    for msg_name in dbc_handler.get_all_message_names():
        can_id, data = dbc_handler.encode_message(msg_name)
        if can_id:
            hex_data = ' '.join(f'{b:02X}' for b in data)
            print(f"   - {msg_name}: ID=0x{can_id:X}, Data=[{hex_data}]")
            
            # 验证编码结果
            print(f"     详细分析:")
            message = dbc_handler.get_message_by_name(msg_name)
            for signal in message.signals:
                raw_value = signal.encode_to_raw()
                print(f"       {signal.name}: 物理值={signal.current_value}, 原始值={raw_value}")
    
    # 4. 测试消息解码
    print(f"\n4. 测试消息解码:")
    for msg_name in dbc_handler.get_all_message_names():
        can_id, data = dbc_handler.encode_message(msg_name)
        if can_id:
            decoded = dbc_handler.decode_message(can_id, data)
            print(f"   - {decoded['message_name']}:")
            for signal_name, value in decoded['signals'].items():
                print(f"       {signal_name}: {value}")
    
    # 5. 测试信号值更新
    print(f"\n5. 测试信号值更新:")
    print("   更新 Engine_Speed 从 2500.75 到 3000.0")
    dbc_handler.update_signal_value("Engine_Data", "Engine_Speed", 3000.0)
    
    can_id, data = dbc_handler.encode_message("Engine_Data")
    hex_data = ' '.join(f'{b:02X}' for b in data)
    print(f"   更新后的数据: [{hex_data}]")
    
    decoded = dbc_handler.decode_message(can_id, data)
    print(f"   解码验证: Engine_Speed = {decoded['signals']['Engine_Speed']}")

def test_manual_calculation():
    """手动计算验证"""
    print("\n=== 手动计算验证 ===")
    
    # Engine_Data 消息手动计算
    print("\nEngine_Data 消息手动计算:")
    
    # Engine_Speed: 2500.75, factor=0.25, offset=0
    # raw = (2500.75 - 0) / 0.25 = 10003
    # 10003 = 0x2713, 16位，占用字节0-1
    engine_speed_raw = int((2500.75 - 0) / 0.25)
    print(f"Engine_Speed: 物理值=2500.75, 原始值={engine_speed_raw} (0x{engine_speed_raw:04X})")
    
    # Engine_Torque: 250.5, factor=0.1, offset=-500
    # raw = (250.5 - (-500)) / 0.1 = 7505
    # 7505 = 0x1D51, 16位，占用字节2-3
    engine_torque_raw = int((250.5 - (-500)) / 0.1)
    print(f"Engine_Torque: 物理值=250.5, 原始值={engine_torque_raw} (0x{engine_torque_raw:04X})")
    
    # Throttle_Position: 65.2, factor=0.4, offset=0
    # raw = (65.2 - 0) / 0.4 = 163
    # 163 = 0xA3, 8位，占用字节4
    throttle_raw = int((65.2 - 0) / 0.4)
    print(f"Throttle_Position: 物理值=65.2, 原始值={throttle_raw} (0x{throttle_raw:02X})")
    
    # 预期的字节数组 (小端序)
    expected_bytes = [
        engine_speed_raw & 0xFF,        # 字节0: 0x13
        (engine_speed_raw >> 8) & 0xFF, # 字节1: 0x27
        engine_torque_raw & 0xFF,       # 字节2: 0x51
        (engine_torque_raw >> 8) & 0xFF,# 字节3: 0x1D
        throttle_raw & 0xFF,            # 字节4: 0xA3
        0,                              # 字节5: 0x00
        0,                              # 字节6: 0x00
        0                               # 字节7: 0x00
    ]
    
    expected_hex = ' '.join(f'{b:02X}' for b in expected_bytes)
    print(f"预期字节数组: [{expected_hex}]")

if __name__ == "__main__":
    test_dbc_parsing()
    test_manual_calculation()
