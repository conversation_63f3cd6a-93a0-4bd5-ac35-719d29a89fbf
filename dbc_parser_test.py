#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DBC报文解析测试工具
用于解析CAN-FD报文数据
"""

import struct
from typing import Dict, Any, List, Tuple


class DBCSignal:
    """DBC信号定义"""
    def __init__(self, name: str, start_bit: int, length: int, byte_order: str, 
                 is_signed: bool, factor: float, offset: float, 
                 min_val: float, max_val: float, unit: str):
        self.name = name
        self.start_bit = start_bit
        self.length = length
        self.byte_order = byte_order  # 'big_endian' or 'little_endian'
        self.is_signed = is_signed
        self.factor = factor
        self.offset = offset
        self.min_val = min_val
        self.max_val = max_val
        self.unit = unit


class DBCMessage:
    """DBC消息定义"""
    def __init__(self, msg_id: int, name: str, dlc: int, sender: str):
        self.msg_id = msg_id
        self.name = name
        self.dlc = dlc
        self.sender = sender
        self.signals: List[DBCSignal] = []
    
    def add_signal(self, signal: DBCSignal):
        """添加信号"""
        self.signals.append(signal)


class DBCParser:
    """DBC解析器"""
    
    def __init__(self):
        self.messages: Dict[int, DBCMessage] = {}
        self._init_zone_fl_nomi_01()
    
    def _init_zone_fl_nomi_01(self):
        """初始化Zone_FL_NOMI_01消息定义"""
        # BO_ 549 Zone_FL_NOMI_01: 48 ZONE_FTM
        # 但实际接收到的是225 (0xE1)，所以使用225作为消息ID
        msg = DBCMessage(225, "Zone_FL_NOMI_01", 48, "ZONE_FTM")
        
        # 添加所有信号定义
        # 注意：DBC格式中的@0+表示big endian，@1+表示little endian
        signals = [
            # SG_ HALO_LIGHT_DIRECTION : 31|3@0+ (1,0) [0|7] ""  NOMI
            DBCSignal("HALO_LIGHT_DIRECTION", 31, 3, "big_endian", False, 1, 0, 0, 7, ""),
            
            # SG_ HALO_LIGHT_DURATION : 95|16@0+ (1,0) [0|65535] "ms"  NOMI
            DBCSignal("HALO_LIGHT_DURATION", 95, 16, "big_endian", False, 1, 0, 0, 65535, "ms"),
            
            # SG_ HALO_LIGHT_VOLUME : 47|7@0+ (1,0) [0|127] "%"  NOMI
            DBCSignal("HALO_LIGHT_VOLUME", 47, 7, "big_endian", False, 1, 0, 0, 127, "%"),
            
            # SG_ HALO_MUSIC_BEAT : 55|1@0+ (1,0) [0|1] ""  NOMI
            DBCSignal("HALO_MUSIC_BEAT", 55, 1, "big_endian", False, 1, 0, 0, 1, ""),
            
            # SG_ HALO_MUSIC_BPM : 79|16@0+ (1,0) [0|65535] ""  NOMI
            DBCSignal("HALO_MUSIC_BPM", 79, 16, "big_endian", False, 1, 0, 0, 65535, ""),
            
            # SG_ HALO_MUSIC_COLOR : 54|7@0+ (1,0) [0|127] ""  NOMI
            DBCSignal("HALO_MUSIC_COLOR", 54, 7, "big_endian", False, 1, 0, 0, 127, ""),
            
            # SG_ HALO_MUSIC_VOLUME : 63|7@0+ (1,0) [0|127] ""  NOMI
            DBCSignal("HALO_MUSIC_VOLUME", 63, 7, "big_endian", False, 1, 0, 0, 127, ""),
            
            # SG_ HALO_WORK_MODE : 5|1@0+ (1,0) [0|1] ""  NOMI
            DBCSignal("HALO_WORK_MODE", 5, 1, "big_endian", False, 1, 0, 0, 1, ""),
            
            # SG_ HALO_WORK_SELECTOR : 15|8@0+ (1,0) [0|255] ""  NOMI
            DBCSignal("HALO_WORK_SELECTOR", 15, 8, "big_endian", False, 1, 0, 0, 255, ""),
            
            # SG_ NOMI_BackLiReq : 23|7@0+ (1,0) [0|100] "%"  NOMI
            DBCSignal("NOMI_BackLiReq", 23, 7, "big_endian", False, 1, 0, 0, 100, "%"),
            
            # SG_ NOMI_OnOffCmd : 7|2@0+ (1,0) [0|3] ""  NOMI
            DBCSignal("NOMI_OnOffCmd", 7, 2, "big_endian", False, 1, 0, 0, 3, ""),
            
            # SG_ NOMI_VideoSrcReq : 27|2@0+ (1,0) [0|3] ""  NOMI
            DBCSignal("NOMI_VideoSrcReq", 27, 2, "big_endian", False, 1, 0, 0, 3, ""),
        ]
        
        for signal in signals:
            msg.add_signal(signal)
        
        self.messages[225] = msg
    
    def extract_signal_value(self, data: bytes, signal: DBCSignal) -> int:
        """从数据中提取信号值 - 最终修复版本"""
        start_bit = signal.start_bit
        length = signal.length

        # 基于测试结果，我发现了正确的DBC位提取规则：
        # DBC使用大端序位编号，但需要正确理解位的布局

        if signal.byte_order == "big_endian":
            value = 0

            # 从起始位开始，向低位方向提取length个位
            for i in range(length):
                bit_pos = start_bit - i
                if bit_pos < 0:
                    break

                # 计算字节索引和字节内位索引
                byte_index = bit_pos // 8
                bit_in_byte = bit_pos % 8

                if byte_index < len(data):
                    # 从字节中提取位值（位0是LSB，位7是MSB）
                    bit_value = (data[byte_index] >> bit_in_byte) & 1
                    # 构建最终值（MSB在前）
                    value = (value << 1) | bit_value

            return value
        else:
            # 小端序处理
            value = 0
            for i in range(length):
                bit_pos = start_bit + i
                byte_index = bit_pos // 8
                bit_in_byte = bit_pos % 8

                if byte_index < len(data):
                    bit_value = (data[byte_index] >> bit_in_byte) & 1
                    value |= (bit_value << i)

            return value
    
    def decode_message(self, msg_id: int, data: bytes) -> Dict[str, Any]:
        """解码CAN消息"""
        if msg_id not in self.messages:
            return {"error": f"Unknown message ID: {msg_id}"}
        
        message = self.messages[msg_id]
        result = {
            "message_id": msg_id,
            "message_name": message.name,
            "dlc": len(data),
            "signals": {}
        }
        
        for signal in message.signals:
            try:
                raw_value = self.extract_signal_value(data, signal)
                # 应用比例因子和偏移量
                physical_value = raw_value * signal.factor + signal.offset
                
                result["signals"][signal.name] = {
                    "raw_value": raw_value,
                    "physical_value": physical_value,
                    "unit": signal.unit,
                    "valid_range": f"[{signal.min_val}, {signal.max_val}]"
                }
            except Exception as e:
                result["signals"][signal.name] = {"error": str(e)}
        
        return result


def parse_canfd_log_line(log_line: str) -> Tuple[float, int, bytes]:
    """解析CAN-FD日志行 - 正确版本"""
    # 格式: 0.000000 CANFD 1 225 Rx 0 0 d 14 48 40 00 c8 04 00 00...
    # 解析: 48是DLC, 40 00 c8 04...是实际CAN数据
    parts = log_line.strip().split()

    timestamp = float(parts[0])  # 时间戳
    msg_id = int(parts[3])       # CAN ID (225十进制)
    dlc_hex = parts[9]           # DLC (48 hex)
    dlc = int(dlc_hex, 16)       # 转换为十进制

    print(f"解析: 时间={timestamp}, ID={msg_id}, DLC=0x{dlc:02X}({dlc})")

    # 实际的CAN数据从第10个元素开始
    data_hex = parts[10:]
    data_bytes = bytes([int(hex_str, 16) for hex_str in data_hex])

    print(f"数据: {' '.join([f'0x{b:02X}' for b in data_bytes[:8]])}")  # 只显示前8字节

    return timestamp, msg_id, data_bytes


def print_detailed_analysis(data: bytes):
    """打印详细的字节分析"""
    print("\n=== 详细字节分析 ===")
    print("字节位置  十六进制  二进制        十进制")
    print("-" * 45)
    for i, byte in enumerate(data[:16]):  # 只显示前16字节
        binary = format(byte, '08b')
        print(f"Byte {i:2d}   0x{byte:02X}      {binary}    {byte:3d}")
    if len(data) > 16:
        print(f"... (还有 {len(data)-16} 字节)")
    print()


def analyze_key_signals(decoded: Dict[str, Any]):
    """分析关键信号的含义"""
    print("=== 关键信号分析 ===")
    signals = decoded.get("signals", {})

    # NOMI_OnOffCmd 分析
    if "NOMI_OnOffCmd" in signals:
        cmd_val = signals["NOMI_OnOffCmd"]["raw_value"]
        cmd_meanings = {0: "关闭", 1: "开启", 2: "待机", 3: "保留"}
        print(f"NOMI开关命令: {cmd_val} ({cmd_meanings.get(cmd_val, '未知')})")

    # HALO_WORK_MODE 分析
    if "HALO_WORK_MODE" in signals:
        mode_val = signals["HALO_WORK_MODE"]["raw_value"]
        mode_meanings = {0: "关闭", 1: "开启"}
        print(f"HALO工作模式: {mode_val} ({mode_meanings.get(mode_val, '未知')})")

    # HALO_WORK_SELECTOR 分析
    if "HALO_WORK_SELECTOR" in signals:
        selector_val = signals["HALO_WORK_SELECTOR"]["raw_value"]
        print(f"HALO工作选择器: {selector_val} (0x{selector_val:02X})")

    # 背光亮度分析
    if "NOMI_BackLiReq" in signals:
        backlight_val = signals["NOMI_BackLiReq"]["physical_value"]
        print(f"背光亮度请求: {backlight_val}%")

    # 音量分析
    if "HALO_LIGHT_VOLUME" in signals:
        volume_val = signals["HALO_LIGHT_VOLUME"]["physical_value"]
        print(f"HALO灯光音量: {volume_val}%")

    print()


def debug_bit_extraction():
    """调试位提取算法"""
    # 原始数据: 14 48 40 00 C8 04
    data = bytes([0x14, 0x48, 0x40, 0x00, 0xC8, 0x04])

    print("=== 位提取调试 ===")
    print("原始数据:", " ".join([f"0x{b:02X}" for b in data]))
    print()

    # 重新理解：专业工具显示NOMI_BackLiReq=100，原始值0x64
    # 0x64 = 100，这个值应该在数据中的某个位置
    # 让我找找哪里有0x64或者能组合成100的位

    print("=== 寻找期望值 ===")
    print("寻找 NOMI_OnOffCmd = 1")
    print("寻找 NOMI_BackLiReq = 100 (0x64)")
    print("寻找 NOMI_VideoSrcReq = 1")
    print()

    # 检查是否有字节包含这些值
    for i, byte in enumerate(data):
        print(f"Byte {i}: 0x{byte:02X} ({byte})")
        if byte == 1:
            print("  -> 可能是 NOMI_OnOffCmd 或 NOMI_VideoSrcReq")
        if byte == 100:
            print("  -> 可能是 NOMI_BackLiReq")

    print()
    print("=== 重新分析原始数据 ===")
    # 也许我需要重新检查原始报文数据
    # 专业工具解析出NOMI_BackLiReq=100，但我在前6字节中没看到0x64
    # 让我检查完整的50字节数据

    full_data_hex = "14 48 40 00 c8 04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00"
    full_data = bytes([int(x, 16) for x in full_data_hex.split()])

    print("完整数据长度:", len(full_data))
    for i, byte in enumerate(full_data):
        if byte != 0:  # 只显示非零字节
            print(f"Byte {i}: 0x{byte:02X} ({byte})")
            if byte == 100:
                print(f"  -> 找到100! 在字节{i}")

    # 也许问题在于我对原始数据的解析有误
    print()
    print("=== 重新检查原始报文 ===")
    # 让我重新解析原始报文字符串


def check_original_data():
    """检查原始数据解析是否正确"""
    print("=== 检查原始数据解析 ===")

    # 原始报文字符串
    canfd_log = "0.000000 CANFD 1 225 Rx 0 0 d 14 48 40 00 c8 04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00"

    # 解析数据部分
    parts = canfd_log.strip().split()
    print("所有部分:", parts)
    print()

    # 数据从第8个元素开始
    data_hex = parts[8:]
    print("数据部分:", data_hex)
    print()

    # 转换为字节
    data_bytes = bytes([int(hex_str, 16) for hex_str in data_hex])
    print("字节数据:", " ".join([f"0x{b:02X}" for b in data_bytes]))
    print("数据长度:", len(data_bytes))
    print()

    # 检查是否有100 (0x64)
    for i, byte in enumerate(data_bytes):
        if byte == 100:
            print(f"找到100在字节{i}: 0x{byte:02X}")
        elif byte == 0x64:
            print(f"找到0x64在字节{i}: {byte}")

    # 也许专业工具的原始数据和我解析的不一样？
    # 让我手动构造一个包含期望值的数据来测试算法
    print()
    print("=== 构造测试数据 ===")
    # 如果NOMI_BackLiReq=100在起始位23，位宽7
    # 我需要在正确的位置放置100这个值

    test_data = bytearray(50)  # 50字节全零

    # 在第2字节放置100
    test_data[2] = 100
    # 在第0字节设置一些位用于其他信号
    test_data[0] = 0x80  # 设置最高位，看看能否得到NOMI_OnOffCmd=1

    print("测试数据:", " ".join([f"0x{b:02X}" for b in test_data[:6]]))

    return test_data


def create_correct_test_data():
    """根据专业工具结果创建正确的测试数据"""
    print("=== 根据专业工具结果创建测试数据 ===")

    # 专业工具的结果：
    expected_results = {
        "HALO_WORK_MODE": (0, 5, 1),  # (值, 起始位, 位宽)
        "NOMI_OnOffCmd": (1, 7, 2),
        "HALO_WORK_SELECTOR": (0, 15, 8),
        "NOMI_BackLiReq": (100, 23, 7),
        "NOMI_VideoSrcReq": (1, 27, 2),
        "HALO_LIGHT_DIRECTION": (0, 31, 3),
        "HALO_LIGHT_VOLUME": (0, 47, 7),
    }

    # 创建一个50字节的数据数组
    test_data = bytearray(50)

    # 手动设置每个信号的值
    for signal_name, (value, start_bit, bit_width) in expected_results.items():
        print(f"设置 {signal_name}: 值={value}, 起始位={start_bit}, 位宽={bit_width}")

        # 计算应该在哪个字节
        byte_index = start_bit // 8
        bit_in_byte = start_bit % 8

        print(f"  -> 字节{byte_index}, 位{bit_in_byte}")

        # 简单设置：直接在对应字节设置值（这是一个简化的方法）
        if signal_name == "NOMI_BackLiReq":
            # 100需要7位，放在字节2-3
            test_data[2] = 100  # 直接设置
        elif signal_name == "NOMI_OnOffCmd":
            # 值1需要2位，在字节0
            test_data[0] |= (1 << 6)  # 设置位6-7
        elif signal_name == "NOMI_VideoSrcReq":
            # 值1需要2位，在字节3
            test_data[3] |= (1 << 3)  # 设置位3-4

    print()
    print("构造的测试数据:", " ".join([f"0x{b:02X}" for b in test_data[:6]]))
    return test_data


def test_with_constructed_data():
    """使用构造的数据测试解析"""
    print("\n=== 使用构造数据测试解析 ===")

    # 创建测试数据
    test_data = create_correct_test_data()

    # 使用解析器测试
    parser = DBCParser()
    decoded = parser.decode_message(225, test_data)

    print("\n解析结果:")
    for signal_name, signal_data in decoded["signals"].items():
        if "error" not in signal_data:
            raw_val = signal_data["raw_value"]
            phy_val = signal_data["physical_value"]
            print(f"{signal_name}: {raw_val} ({phy_val})")


def teach_dbc_analysis():
    """教学：如何手动分析DBC信号位置"""
    print("=== DBC信号位置分析教学 ===\n")

    # 我们的数据
    data = bytes([0x40, 0x00, 0xC8, 0x04, 0x00, 0x00, 0x00, 0x00])

    print("📊 原始数据:")
    print("字节:  ", " ".join([f"Byte{i}" for i in range(len(data))]))
    print("十六进制:", " ".join([f"0x{b:02X}" for b in data]))
    print("十进制:  ", " ".join([f"{b:3d}" for b in data]))
    print("二进制:  ", " ".join([f"{b:08b}" for b in data]))
    print()

    print("🔍 位编号图 (DBC标准):")
    print("字节0的位编号: 7  6  5  4  3  2  1  0")
    print("字节1的位编号: 15 14 13 12 11 10 9  8")
    print("字节2的位编号: 23 22 21 20 19 18 17 16")
    print("字节3的位编号: 31 30 29 28 27 26 25 24")
    print()

    # 手动分析几个关键信号
    print("🎯 手动信号分析示例:")
    print()

    # 1. NOMI_OnOffCmd: 起始位7, 位宽2
    print("1️⃣ NOMI_OnOffCmd (起始位7, 位宽2)")
    print("   位置: 位7-6 (字节0的最高2位)")
    print("   字节0 = 0x40 = 01000000")
    print("   位7 = 0, 位6 = 1")
    print("   值 = 01 (二进制) = 1 (十进制) ✅")
    print()

    # 2. HALO_WORK_MODE: 起始位5, 位宽1
    print("2️⃣ HALO_WORK_MODE (起始位5, 位宽1)")
    print("   位置: 位5 (字节0的第6位)")
    print("   字节0 = 0x40 = 01000000")
    print("   位5 = 0")
    print("   值 = 0 ✅")
    print()

    # 3. NOMI_BackLiReq: 起始位23, 位宽7
    print("3️⃣ NOMI_BackLiReq (起始位23, 位宽7)")
    print("   位置: 位23-17 (跨字节2-3)")
    print("   字节2 = 0xC8 = 11001000")
    print("   字节3 = 0x04 = 00000100")
    print("   位23-17 = 1100100 (二进制) = 100 (十进制) ✅")
    print()

    # 4. NOMI_VideoSrcReq: 起始位27, 位宽2
    print("4️⃣ NOMI_VideoSrcReq (起始位27, 位宽2)")
    print("   位置: 位27-26 (字节3)")
    print("   字节3 = 0x04 = 00000100")
    print("   位27-26 = 01 (二进制) = 1 (十进制) ✅")
    print()

    print("💡 关键规则总结:")
    print("1. DBC位编号从0开始，每字节8位")
    print("2. 大端序：从起始位向低位方向取值")
    print("3. 位7是字节的最高位(MSB)，位0是最低位(LSB)")
    print("4. 跨字节信号需要组合多个字节的位")


def step_by_step_extraction():
    """逐步演示信号提取过程"""
    print("\n=== 逐步信号提取演示 ===\n")

    data = bytes([0x40, 0x00, 0xC8, 0x04])

    print("🔧 手动提取 NOMI_BackLiReq (起始位23, 位宽7):")
    print()

    # 步骤1: 确定涉及的字节
    start_bit = 23
    length = 7
    print(f"步骤1: 起始位{start_bit}, 长度{length}")
    print(f"涉及位: {start_bit} 到 {start_bit-length+1}")
    print(f"涉及字节: {start_bit//8} 到 {(start_bit-length+1)//8}")
    print()

    # 步骤2: 提取每一位
    print("步骤2: 逐位提取")
    value = 0
    for i in range(length):
        bit_pos = start_bit - i
        byte_index = bit_pos // 8
        bit_in_byte = bit_pos % 8

        if byte_index < len(data):
            bit_value = (data[byte_index] >> bit_in_byte) & 1
            value = (value << 1) | bit_value

            print(f"  位{bit_pos}: 字节{byte_index}[位{bit_in_byte}] = {bit_value}")
            print(f"    字节{byte_index} = 0x{data[byte_index]:02X} = {data[byte_index]:08b}")
            print(f"    当前累积值 = {value}")
        print()

    print(f"最终结果: {value} ✅")


def main():
    """主测试函数"""
    print("=== DBC信号分析教学 ===\n")

    # 教学模式
    teach_dbc_analysis()
    step_by_step_extraction()

    print("\n" + "="*60)
    print("现在你学会了如何手动分析DBC信号位置！")
    print("="*60)

    # 初始化解析器
    parser = DBCParser()

    # 测试数据
    canfd_log = "0.000000 CANFD 1 225 Rx 0 0 d 14 48 40 00 c8 04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00"

    print("原始CAN-FD报文:")
    print(canfd_log)
    print()

    # 解析日志行
    timestamp, msg_id, data = parse_canfd_log_line(canfd_log)

    print(f"时间戳: {timestamp}")
    print(f"消息ID: {msg_id} (0x{msg_id:X})")
    print(f"数据长度: {len(data)} 字节")
    print(f"原始数据: {' '.join([f'{b:02X}' for b in data])}")

    # 打印详细字节分析
    print_detailed_analysis(data)

    # 解码消息
    decoded = parser.decode_message(msg_id, data)

    print("=== 解码结果 ===")
    print(f"消息名称: {decoded['message_name']}")
    print(f"消息ID: {decoded['message_id']}")
    print(f"数据长度: {decoded['dlc']} 字节")
    print()

    print("信号解析:")
    print("-" * 80)
    print(f"{'信号名称':<20} {'原始值':<10} {'物理值':<15} {'单位':<8} {'有效范围'}")
    print("-" * 80)

    for signal_name, signal_data in decoded["signals"].items():
        if "error" in signal_data:
            print(f"{signal_name:<20} ERROR: {signal_data['error']}")
        else:
            raw_val = signal_data["raw_value"]
            phy_val = signal_data["physical_value"]
            unit = signal_data["unit"]
            range_str = signal_data["valid_range"]
            print(f"{signal_name:<20} {raw_val:<10} {phy_val:<15} {unit:<8} {range_str}")

    print("-" * 80)

    # 分析关键信号
    analyze_key_signals(decoded)


if __name__ == "__main__":
    main()
