import traceback

import cgitb

import multiprocessing
import os
import sys
import time

from PyQt5 import QtCore, QtWidgets
from PyQt5.QtCore import QLockFile, QDir, Qt
from PyQt5.QtGui import QIcon, QFont, QFontDatabase
from PyQt5.QtWidgets import QApplication

from common.AppConfig import app_config
from common.LogUtils import start_log
from qt_material import apply_stylesheet
from utils.ScreenManager import screen_manager
from utils.SystemManager import system_manager
from view.LoginView import LoginView


def set_global_font(font_path, point_size=14):
    # 加载字体文件
    font_id = QFontDatabase.addApplicationFont(font_path)

    print(f"font_id id={font_id}")

    if font_id < 0:
        print("Error loading font file.")
        return

    # 获取字体系列名称
    font_family = QFontDatabase.applicationFontFamilies(font_id)[0]
    print(f"font_family id={font_family}")

    # 设置全局字体
    app.setFont(QFont(font_family, point_size))

import traceback
import threading
from datetime import datetime
from typing import Type, Any, Optional
from pathlib import Path

class GlobalExceptionHandler:
    """全局异常处理器"""
  
    def __init__(self, 
                 app_name: str = "Application",
                 enable_crash_report: bool = True,
                 enable_error_dialog: bool = False,
                 max_error_logs: int = 100):
        self.app_name = app_name
        self.enable_crash_report = enable_crash_report
        self.enable_error_dialog = enable_error_dialog
        self.max_error_logs = max_error_logs
        self.error_count = 0
        self.last_error_time = 0
        self.error_history = []
        self._lock = threading.Lock()
      
        # 延迟导入，避免循环依赖
        self._logger = None
      
    @property
    def logger(self):
        """懒加载日志器"""
        if self._logger is None:
            try:
                from common.LogUtils import logger
                self._logger = logger
            except ImportError:
                import logging
                self._logger = logging.getLogger(__name__)
        return self._logger
  
    def __call__(self, exctype: Type[BaseException], 
                 value: BaseException, 
                 traceback_obj: Any) -> None:
        """异常处理主函数"""
        try:
            self._handle_exception(exctype, value, traceback_obj)
        except Exception as e:
            # 异常处理器本身出错时的最后防线
            self._emergency_handler(e, exctype, value, traceback_obj)
  
    def _handle_exception(self, exctype: Type[BaseException], 
                         value: BaseException, 
                         traceback_obj: Any) -> None:
        """处理异常的核心逻辑"""
      
        # 防止异常洪水
        if self._is_error_flooding():
            return
      
        # 收集异常信息
        error_info = self._collect_error_info(exctype, value, traceback_obj)
      
        # 记录异常
        self._log_exception(error_info)
      
        # 根据异常类型采取不同策略
        self._handle_by_exception_type(exctype, value, error_info)
      
        # 生成崩溃报告
        if self.enable_crash_report:
            self._generate_crash_report(error_info)
      
        # 显示错误对话框（GUI应用）
        if self.enable_error_dialog:
            self._show_error_dialog(error_info)
      
        # 更新错误统计
        self._update_error_stats(exctype, value)
      
        # 尝试优雅退出
        self._graceful_shutdown(exctype, value)
  
    def _is_error_flooding(self) -> bool:
        """检测是否为异常洪水，防止日志爆炸"""
        current_time = time.time()
      
        with self._lock:
            # 如果1秒内超过5个异常，认为是异常洪水
            if current_time - self.last_error_time < 1:
                self.error_count += 1
                if self.error_count > 5:
                    return True
            else:
                self.error_count = 1
          
            self.last_error_time = current_time
            return False
  
    def _collect_error_info(self, exctype: Type[BaseException], 
                           value: BaseException, 
                           traceback_obj: Any) -> dict:
        """收集详细的错误信息"""
      
        # 格式化异常信息
        error_traceback = ''.join(traceback.format_exception(exctype, value, traceback_obj))
      
        # 获取异常发生的具体位置
        tb_frame = traceback_obj.tb_frame if traceback_obj else None
        local_vars = {}
        if tb_frame:
            try:
                # 安全地获取局部变量（避免敏感信息）
                local_vars = {k: str(v)[:100] for k, v in tb_frame.f_locals.items() 
                             if not k.startswith('_') and not callable(v)}
            except Exception:
                local_vars = {"error": "无法获取局部变量"}
      
        return {
            'timestamp': datetime.now().isoformat(),
            'exception_type': exctype.__name__,
            'exception_message': str(value),
            'traceback': error_traceback,
            'local_variables': local_vars,
            'thread_info': {
                'current_thread': threading.current_thread().name,
                'active_threads': [t.name for t in threading.enumerate()],
                'thread_count': threading.active_count()
            },
            'system_info': {
                'python_version': sys.version,
                'platform': sys.platform,
                'cwd': os.getcwd(),
                'pid': os.getpid()
            }
        }
  
    def _log_exception(self, error_info: dict) -> None:
        """记录异常日志"""
        try:
            # 构建详细的错误消息
            # 遍历线程名称
            error_summary = (
                f"{self.app_name} 发生未捕获异常\n"
                f"时间: {error_info['timestamp']}\n"
                f"类型: {error_info['exception_type']}\n"
                f"消息: {error_info['exception_message']}\n"
                f"线程: {error_info['thread_info']['current_thread']}\n"
                f"位置: {self._extract_error_location(error_info['traceback'])}"
            )
          
            # 记录关键异常信息
            self.logger.critical(error_summary)
          
            # 记录完整堆栈
            self.logger.critical(f"完整堆栈信息:\n{error_info['traceback']}")
          
            # 记录局部变量（调试用）
            if error_info['local_variables']:
                self.logger.debug(f"异常发生时的局部变量: {error_info['local_variables']}")


              
        except Exception as e:
            # 日志记录失败时的后备方案
            print(f"日志记录失败: {e}")
            print(f"原始异常: {error_info['exception_type']}: {error_info['exception_message']}")
  
    def _extract_error_location(self, traceback_str: str) -> str:
        """从堆栈中提取关键的错误位置"""
        lines = traceback_str.strip().split('\n')
        for line in reversed(lines):
            if 'File "' in line and 'line' in line:
                return line.strip()
        return "未知位置"
  
    def _handle_by_exception_type(self, exctype: Type[BaseException], 
                                 value: BaseException, 
                                 error_info: dict) -> None:
        """根据异常类型采取不同的处理策略"""
      
        if issubclass(exctype, KeyboardInterrupt):
            self.logger.info("用户中断程序执行")
            return
      
        elif issubclass(exctype, SystemExit):
            self.logger.info(f"程序正常退出，退出码: {value.code if hasattr(value, 'code') else 'N/A'}")
            return
      
        elif issubclass(exctype, MemoryError):
            self.logger.critical("内存不足，程序即将退出")
            # 可以在这里清理内存、保存重要数据
          
        elif issubclass(exctype, ImportError):
            self.logger.critical(f"模块导入失败: {value}")
            # 可以提示用户安装缺失的依赖
          
        elif issubclass(exctype, (ConnectionError, TimeoutError)):
            self.logger.error(f"网络连接问题: {value}")
            # 可以尝试重连或降级处理
  
    def _generate_crash_report(self, error_info: dict) -> None:
        """生成崩溃报告文件"""
        try:
            crash_dir = Path("crash_reports")
            crash_dir.mkdir(exist_ok=True)
          
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = crash_dir / f"crash_report_{timestamp}.txt"
          
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(f"=== {self.app_name} 崩溃报告 ===\n\n")
                f.write(f"时间: {error_info['timestamp']}\n")
                f.write(f"异常类型: {error_info['exception_type']}\n")
                f.write(f"异常消息: {error_info['exception_message']}\n\n")
                f.write(f"系统信息:\n")
                for key, value in error_info['system_info'].items():
                    f.write(f"  {key}: {value}\n")
                f.write(f"\n线程信息:\n")
                for key, value in error_info['thread_info'].items():
                    f.write(f"  {key}: {value}\n")
                f.write(f"\n完整堆栈:\n{error_info['traceback']}\n")
              
                if error_info['local_variables']:
                    f.write(f"\n局部变量:\n")
                    for key, value in error_info['local_variables'].items():
                        f.write(f"  {key}: {value}\n")
          
            self.logger.info(f"崩溃报告已保存: {report_file}")
          
        except Exception as e:
            self.logger.error(f"生成崩溃报告失败: {e}")
  
    def _show_error_dialog(self, error_info: dict) -> None:
        """显示错误对话框（适用于GUI应用）"""
        try:
            # 这里可以集成不同的GUI框架
            import tkinter as tk
            from tkinter import messagebox
          
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
          
            message = (
                f"程序遇到未处理的错误:\n\n"
                f"错误类型: {error_info['exception_type']}\n"
                f"错误信息: {error_info['exception_message']}\n\n"
                f"详细信息已记录到日志文件中。"
            )
          
            messagebox.showerror(f"{self.app_name} - 错误", message)
            root.destroy()
          
        except Exception:
            # GUI不可用时静默处理
            pass
  
    def _update_error_stats(self, exctype: Type[BaseException], value: BaseException) -> None:
        """更新错误统计信息"""
        with self._lock:
            error_record = {
                'timestamp': datetime.now().isoformat(),
                'type': exctype.__name__,
                'message': str(value)[:100]  # 限制长度
            }
          
            self.error_history.append(error_record)
          
            # 限制历史记录数量
            if len(self.error_history) > self.max_error_logs:
                self.error_history.pop(0)
  
    def _graceful_shutdown(self, exctype: Type[BaseException], value: BaseException) -> None:
        """尝试优雅关闭"""
        if not issubclass(exctype, (KeyboardInterrupt, SystemExit)):
            # 给其他线程一些时间来清理
            time.sleep(0.1)
          
            # 这里可以添加清理逻辑：
            # - 保存未保存的数据
            # - 关闭数据库连接
            # - 清理临时文件
            # - 发送错误通知等
          
            self.logger.info("执行清理操作完成")
  
    def _emergency_handler(self, handler_error: Exception, 
                          original_exctype: Type[BaseException],
                          original_value: BaseException, 
                          original_traceback: Any) -> None:
        """异常处理器自身出错时的紧急处理"""
        try:
            emergency_msg = (
                f"全局异常处理器自身出错!\n"
                f"处理器错误: {type(handler_error).__name__}: {handler_error}\n"
                f"原始异常: {original_exctype.__name__}: {original_value}\n"
                f"时间: {datetime.now().isoformat()}"
            )
          
            # 直接输出到stderr，确保信息不丢失
            print(emergency_msg, file=sys.stderr)
          
            # 尝试写入紧急日志文件
            with open("emergency_error.log", "a", encoding="utf-8") as f:
                f.write(f"\n{'='*50}\n")
                f.write(emergency_msg)
                f.write(f"\n{'='*50}\n")
              
        except Exception:
            # 最后的最后，直接打印
            print("全局异常处理器完全失效!", file=sys.stderr)
      
        finally:
            # 确保调用原始异常处理器
            sys.__excepthook__(original_exctype, original_value, original_traceback)



def setup_global_exception_handler(app_name: str = "MyApplication"):
    """设置全局异常处理器"""
    handler = GlobalExceptionHandler(
        app_name=app_name,
        enable_crash_report=True,
        enable_error_dialog=False,  # GUI应用时可设为True
        max_error_logs=100
    )
  
    sys.excepthook = handler

    # 设置线程异常处理（Python 3.8+）
    if hasattr(threading, 'excepthook'):
        def thread_exception_handler(args):
            handler(args.exc_type, args.exc_value, args.exc_traceback)
        threading.excepthook = thread_exception_handler

    return handler


if __name__ == '__main__':
    multiprocessing.freeze_support()
    file = QLockFile(f'{QDir.tempPath()}/ATEApp.lock')
    file.setStaleLockTime(0)
    if not file.tryLock():
        # 防止多开
        sys.exit(0)
    # 当程序发生异常时，将异常信息以html的格式保存到指定文件夹中
    # sys.excepthook = cgitb.Hook(display=0, logdir=app_config.exception_folder, context=10, format='html')
    app_config.load_global_config()
    filename = '%s.log' % (time.strftime("%Y-%m-%d-%H-%M-%S", time.localtime(time.time())))
    formatter = '%(asctime)s - %(thread)d - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s'
    start_log()
    # test_upload_manager.start_message_center()
    system_manager.check_disk_free_usage()

    # 启动ADB进程监控
    try:
        from adb.AdbProcessMonitor import adb_process_monitor
        adb_process_monitor.start_monitoring()
    except Exception as e:
        print(f"启动ADB进程监控失败: {e}")

    app = QApplication(sys.argv)

    # 启用高DPI支持
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    # 低分辨率下 界面整齐
    screen = app.primaryScreen()
    screen_resolution = screen.size()
    screen_width, screen_height = screen_resolution.width(), screen_resolution.height()
    screen_manager.screen_width = screen_width
    screen_manager.screen_height = screen_height

    # 设置全局主题样式
    apply_stylesheet(app, theme='dark_custom.xml')
    set_global_font("qt_material/fonts/notosans/NotoSansSC-Regular.ttf", 11)
    icon = QIcon(os.path.join(os.getcwd(), "./res/HWTC.ico"))
    app.setWindowIcon(icon)

    # 设置全局异常处理
    exception_handler = setup_global_exception_handler("HWTreeATE")
    login_window = LoginView()
    login_window.show()
    app.exec_()

