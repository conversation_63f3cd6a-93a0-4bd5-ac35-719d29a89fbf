import os
import time
from datetime import datetime, timezone, timedelta

import psutil
from serial.tools.list_ports import comports

from utils.DiskManager import is_disk_usb_drive


def get_serial_list():
    plist = list(comports())
    ports = {}
    for port in plist:
        ports.update({port.name: port.description})
    return ports


def get_format_time():
    return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time()))


def iterable_int_to_hex(data, fill="0x%02x", separate=""):
    return separate.join([(fill % i).zfill(2) for i in data])


def iterable_int_to_chr(data, separate=""):
    """
    将int转化为chr
    :param separate:
    :param data:
    :return:
    """
    return separate.join([chr(i) for i in data])


def iterable_hex_to_str(data, separate=''):
    """
    将hex转化为str
    :param separate:
    :param data:
    :return:
    """
    return separate.join([str(i) for i in data])


def iterable_byte_to_chr(data, separate=''):
    """
    将byte数组转化为chr
    :param data: 原始byte数组
    :param separate: 分隔符
    :return: 字符串
    """
    return separate.join([chr(i) for i in data if (0x00 < i < 0xFF)])


def int_to_high_low(value):
    """
    将整数转化为高低位
    :param value:
    :return:
    """
    high = ((value >> 8) & 0xff)
    low = (value & 0xff)
    return high, low


def int_to_low_high(value):
    """
    将整数转化为低高位
    :param value:
    :return:
    """
    low = (value & 0xff)
    high = ((value >> 8) & 0xff)
    return low, high


def high_low_to_int(high, low):
    """
    将两个字节的高低位转化为整数
    :param high:
    :param low:
    :return:
    """
    value = low | (high << 8)
    return value


def low_high_to_int(low, high):
    """
    将两个字节的低高位转化为整数
    :param low:
    :param high:
    :return:
    """
    value = low | (high << 8)
    return value


def get_tz_date():
    tz = timezone(timedelta(hours=+8))
    fmt = '%Y-%m-%dT%H:%M:%S.%f%z'
    zoned_time = datetime.now(tz)
    tz_date = zoned_time.strftime(fmt)
    return tz_date


def get_timestamp():
    timestamp = int(round(time.time() * 1000))
    return timestamp


def get_process_pid(process_name):
    for proc in psutil.process_iter(['pid', 'name']):
        if proc.info['name'] == process_name:
            return proc.info['pid']
    return None


def listdir(directory):
    """Returns list of nested files and directories for local directory by path

    :param directory: absolute or relative path to local directory
    :return: list nested of file or directory names
    """
    file_names = []
    for filename in os.listdir(directory):
        file_names.append(filename)
    return file_names


def get_workspace_disk_partition():
    disks = psutil.disk_partitions()
    for disk in disks:
        if disk.device.__contains__("D") and not is_disk_usb_drive("D:"):
            return disk.device

    return None

def hex_string_to_bytes(hex_str):
    """将带空格分隔的16进制字符串转换为字节数组"""
    # 移除空格并转换为字节
    return bytes.fromhex(hex_str.replace(' ', ''))



if __name__ == '__main__':
    low, high = int_to_low_high(2900)
    print(f"low={low}, high={high}")

    crc = ~(0x55 + 0xAA + 0x01 + low + high + 0x00 + 0x00) & 0xFF
    print(crc)

    value = low_high_to_int(0xF4, 0x01)
    print(f"value={value}")

    data = [170, 85, 2, 50, 0, 100, 25, 79]

    value = low_high_to_int(50, 0)
    print(f"value={value}")

    value = low_high_to_int(100, 25)
    print(f"value={value}")

    # 示例使用
    input_str = "63 74 6C 5F 63 6D 64 3A 6C 6F 63 6B 5F 63 6C 6F 73 65 0D 0A"
    byte_array = hex_string_to_bytes(input_str)
    print("字节数组:", byte_array)
