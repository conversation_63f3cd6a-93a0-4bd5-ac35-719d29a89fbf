{0: Frame(name='AMP_03', arbitration_id=ArbitrationId(id=918, extended=False), size=64, transmitters=['AMP'], is_complex_multiplexed=False, is_fd=True, comment='', signals=[Signal(name='DSP1_STS', start_bit=120, size=32, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='DSP1 Internal Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value=Decimal('0'), min=Decimal('0'), max=Decimal('4294967295')), Signal(name='DSP1_STS1', start_bit=152, size=32, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='DSP1 Internal Status2', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value=Decimal('0'), min=Decimal('0'), max=Decimal('4294967295')), Signal(name='DSP1_STS2', start_bit=184, size=32, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='DSP1 Internal Status2', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value=Decimal('0'), min=Decimal('0'), max=Decimal('4294967295')), Signal(name='DSP1_TEMP', start_bit=24, size=16, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='DSP1 Temperature Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value=Decimal('0'), min=Decimal('0'), max=Decimal('65535')), Signal(name='DSP1_USAGE', start_bit=89, size=7, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='%', receivers=['ZONE_FTM'], comment='DSP1 usage', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value=Decimal('0'), min=Decimal('0'), max=Decimal('127')), Signal(name='DSP2H_STS', start_bit=312, size=32, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='DSP2H Internal Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value=Decimal('0'), min=Decimal('0'), max=Decimal('4294967295')), Signal(name='DSP2H_STS1', start_bit=344, size=32, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='DSP2H Internal Status1', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value=Decimal('0'), min=Decimal('0'), max=Decimal('4294967295')), Signal(name='DSP2H_STS2', start_bit=376, size=32, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='DSP2H Internal Status2', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value=Decimal('0'), min=Decimal('0'), max=Decimal('4294967295')), Signal(name='DSP2H_TEMP', start_bit=56, size=16, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='DSP2H Temperature Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value=Decimal('0'), min=Decimal('0'), max=Decimal('65535')), Signal(name='DSP2H_USAGE', start_bit=105, size=7, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='%', receivers=['ZONE_FTM'], comment='DSP2H usage', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value=Decimal('0'), min=Decimal('0'), max=Decimal('100')), Signal(name='DSP2_STS', start_bit=216, size=32, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='DSP2 Internal Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value=Decimal('0'), min=Decimal('0'), max=Decimal('4294967295')), Signal(name='DSP2_STS1', start_bit=248, size=32, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='DSP1 Internal Status1', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value=Decimal('0'), min=Decimal('0'), max=Decimal('4294967295')), Signal(name='DSP2_STS2', start_bit=280, size=32, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='DSP2 Internal Status2', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value=Decimal('0'), min=Decimal('0'), max=Decimal('4294967295')), Signal(name='DSP2_TEMP', start_bit=40, size=16, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='DSP2 Temperature Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value=Decimal('0'), min=Decimal('0'), max=Decimal('65535')), Signal(name='DSP2_USAGE', start_bit=97, size=7, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='%', receivers=['ZONE_FTM'], comment='DSP2 usage', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value=Decimal('0'), min=Decimal('0'), max=Decimal('100')), Signal(name='DSP3_STS', start_bit=408, size=32, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='DSP3 Internal Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value=Decimal('0'), min=Decimal('0'), max=Decimal('4294967295')), Signal(name='DSP3_STS1', start_bit=440, size=32, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='DSP3 Internal Status1', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value=Decimal('0'), min=Decimal('0'), max=Decimal('4294967295')), Signal(name='DSP3_STS2', start_bit=472, size=32, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='DSP3 Internal Status2', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value=Decimal('0'), min=Decimal('0'), max=Decimal('4294967295')), Signal(name='DSP3_TEMP', start_bit=72, size=16, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='DSP3 Temperature Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value=Decimal('0'), min=Decimal('0'), max=Decimal('65535')), Signal(name='DSP3_USAGE', start_bit=113, size=7, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='%', receivers=['ZONE_FTM'], comment='DSP3 usage', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value=Decimal('0'), min=Decimal('0'), max=Decimal('100')), Signal(name='InputVolt', start_bit=3, size=5, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='V', receivers=['ZONE_FTM'], comment='Input Voltage', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value=Decimal('0'), min=Decimal('0'), max=Decimal('31')), Signal(name='NCU_TEMP', start_bit=8, size=16, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='MCU Temperature Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value=Decimal('0'), min=Decimal('0'), max=Decimal('65535'))], mux_names={}, attributes={'GenMsgSendType': 'Cyclic', 'GenMsgILSupport': 'yes', 'GenMsgCycleTime': '1000', 'CANFD_BRS': '1', 'VFrameFormat': 'StandardCAN_FD'}, receivers=['ZONE_FTM'], signalGroups=[], cycle_time=1000, is_j1939=False)}