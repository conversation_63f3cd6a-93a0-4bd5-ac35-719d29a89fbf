{0: Frame(name='AMP_02', arbitration_id=ArbitrationId(id=917, extended=False), size=64, transmitters=['AMP'], is_complex_multiplexed=False, is_fd=True, comment='', signals=[Signal(name='A2B1_TDM_Lost_Times', start_bit=88, size=8, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='A2B1 TDM lost times', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('255')), Signal(name='A2B1_TDM_State', start_bit=104, size=1, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='A2B1 TDM Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('1')), Signal(name='A2B2_TDM_Lost_Times', start_bit=96, size=8, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='A2B2 TDM lost times', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('255')), Signal(name='A2B2_TDM_State', start_bit=105, size=1, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='A2B2 TDM Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('1')), Signal(name='A2B3_TDM_Lost_Times', start_bit=272, size=8, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='A2B2 TDM lost times', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('255')), Signal(name='A2B3_TDM_State', start_bit=287, size=1, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='A2B2 TDM Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='1', min=Decimal('0'), max=Decimal('1')), Signal(name='ADC1_STS', start_bit=360, size=32, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment=None, multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('4294967295')), Signal(name='ADC2_STS', start_bit=328, size=32, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment=None, multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('4294967295')), Signal(name='AMP_Dsp1HeartBeat', start_bit=240, size=32, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='DSP1 heartbeat', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('4294967295')), Signal(name='AMP_Dsp2HHeartBeat', start_bit=288, size=32, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='DSP2H heartbeat', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('4294967295')), Signal(name='AMP_Dsp2HeartBeat', start_bit=24, size=32, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='DSP2 heartbeat', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('4294967295')), Signal(name='AMP_Dsp3HeartBeat', start_bit=56, size=32, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='DSP3 heartbeat', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('4294967295')), Signal(name='Audio_status', start_bit=106, size=4, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='Audio Amplifier Initialization Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('15')), Signal(name='Chip_10_comm_status', start_bit=239, size=1, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='Amplifier chip 10 Communication Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='1', min=Decimal('0'), max=Decimal('1')), Signal(name='Chip_10_status', start_bit=228, size=4, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='Amplifier chip 10 Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('15')), Signal(name='Chip_1_comm_status', start_bit=110, size=1, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='Amplifier chip 1 Communication Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('1')), Signal(name='Chip_1_status', start_bit=192, size=4, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='Amplifier chip 1 Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('15')), Signal(name='Chip_2_comm_status', start_bit=111, size=1, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='Amplifier chip 2 Communication Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('1')), Signal(name='Chip_2_status', start_bit=196, size=4, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='Amplifier chip 2 Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='15', min=Decimal('0'), max=Decimal('15')), Signal(name='Chip_3_comm_status', start_bit=232, size=1, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='Amplifier chip 3 Communication Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='1', min=Decimal('0'), max=Decimal('1')), Signal(name='Chip_3_status', start_bit=200, size=4, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='Amplifier chip 3 Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('15')), Signal(name='Chip_4_comm_status', start_bit=233, size=1, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='Amplifier chip 4 Communication Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='1', min=Decimal('0'), max=Decimal('1')), Signal(name='Chip_4_status', start_bit=204, size=4, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='Amplifier chip 4 Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('15')), Signal(name='Chip_5_comm_status', start_bit=234, size=1, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='Amplifier chip 5 Communication Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('1')), Signal(name='Chip_5_status', start_bit=208, size=4, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='Amplifier chip 5 Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='15', min=Decimal('0'), max=Decimal('15')), Signal(name='Chip_6_comm_status', start_bit=235, size=1, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='Amplifier chip 6 Communication Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='1', min=Decimal('0'), max=Decimal('1')), Signal(name='Chip_6_status', start_bit=212, size=4, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='Amplifier chip 6 Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='15', min=Decimal('0'), max=Decimal('15')), Signal(name='Chip_7_comm_status', start_bit=236, size=1, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='Amplifier chip 7 Communication Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('1')), Signal(name='Chip_7_status', start_bit=216, size=4, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='Amplifier chip 7 Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('15')), Signal(name='Chip_8_comm_status', start_bit=237, size=1, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='Amplifier chip 8 Communication Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('1')), Signal(name='Chip_8_status', start_bit=220, size=4, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='Amplifier chip 8 Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('15')), Signal(name='Chip_9_comm_status', start_bit=238, size=1, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='Amplifier chip 9 Communication Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('1')), Signal(name='Chip_9_status', start_bit=224, size=4, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='Amplifier chip 9 Status', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={1: 'Abnormal', 0: 'Normal'}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='15', min=Decimal('0'), max=Decimal('15')), Signal(name='DAP_Ver', start_bit=112, size=32, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='DAP Version', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('4294967295')), Signal(name='DSP1_Ver', start_bit=144, size=8, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='DSP1 Version', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('255')), Signal(name='DSP2H_Ver', start_bit=320, size=8, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='DSP2H Version', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('255')), Signal(name='DSP2_Ver', start_bit=152, size=8, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='DSP2 Version', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('255')), Signal(name='DSP3_Ver', start_bit=160, size=8, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='DSP3 Version', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('255')), Signal(name='MCU_App_Ver', start_bit=168, size=8, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='MCUAPP Version', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('255')), Signal(name='MCU_AutoSar_Ver', start_bit=176, size=8, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='MCUAutoSar Version', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('255')), Signal(name='MCU_Mcal_Ver', start_bit=184, size=8, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='MCUMcal Version', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigStartValue': '0', 'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('255')), Signal(name='RNC_Cali_ID', start_bit=440, size=8, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='RNC Tuning Parameter XML ID', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('255')), Signal(name='RNC_Host_Lib_Version', start_bit=448, size=8, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='Report Silentium RNC Host and Lib Version', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('255')), Signal(name='RNC_IO_Mute_Cause', start_bit=408, size=32, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='Report IOMute caused by too low signal', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('4294967295')), Signal(name='RNC_Process_Status', start_bit=282, size=5, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='RNC SilArnrProcess function call return value', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={31: 'Reserved', 30: 'Reserved', 29: 'Reserved', 28: 'Reserved', 27: 'Reserved', 26: 'Reserved', 25: 'Reserved', 24: 'Reserved', 23: 'Reserved', 22: 'Reserved', 21: 'Reserved', 20: 'Reserved', 19: 'Reserved', 18: 'Reserved', 17: 'Eol initialization completed correctly', 16: 'Could not decide which MTF golden to fetch', 15: 'Error in STF-sweep section', 14: 'Error in DP section', 13: 'Error in EOL section', 12: 'Error in configuration section', 11: 'Command received from Host is not valid', 10: 'HRs only Mute (1 or more)', 9: 'Mute because of VD parameters TH', 8: 'Mute because of IO bump', 7: 'Error reading from Flash', 6: 'MTF or MTF_EOL completed', 5: 'STF or STF_EOL completed', 4: 'MTF or MTF_EOL in progress', 3: 'STF or STF_EOL in progress', 2: 'ARNC in fade-out operation', 1: 'ARNC in Idle operation', 0: 'Everything OK '}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='31', min=Decimal('0'), max=Decimal('31')), Signal(name='RNC_VD_Mute_Cause', start_bit=392, size=16, is_little_endian=False, is_signed=False, offset=Decimal('0'), factor=Decimal('1'), unit='', receivers=['ZONE_FTM'], comment='Report RNC Mute reason by CAN signal', multiplex=None, mux_value=None, is_float=False, enumeration=None, comments={}, attributes={'GenSigSendType': 'Cyclic'}, values={}, mux_val_grp=[], muxer_for_signal=None, calc_min_for_none=True, calc_max_for_none=True, cycle_time=0, initial_value='0.0', min=Decimal('0'), max=Decimal('65535'))], mux_names={}, attributes={'GenMsgSendType': 'Cyclic', 'GenMsgILSupport': 'yes', 'GenMsgCycleTime': '1000', 'CANFD_BRS': '1', 'VFrameFormat': 'StandardCAN_FD'}, receivers=['ZONE_FTM'], signalGroups=[], cycle_time=1000, is_j1939=False)}