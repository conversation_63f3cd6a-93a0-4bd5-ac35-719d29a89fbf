# -*-coding:utf-8 -*-
"""
# Author     : jid
# Time       : 2025/7/7 10:47
# Description: 
"""


def get_drive_info():
    import win32com.client
    wmi = win32com.client.Dispatch("WbemScripting.SWbemLocator")
    service = wmi.ConnectServer(".", "root\\cimv2")

    # 获取所有逻辑磁盘
    logical_disks = service.ExecQuery("SELECT * FROM Win32_LogicalDisk")

    drive_info = {}
    for disk in logical_disks:
        drive_letter = disk.DeviceID
        # 获取关联的物理媒体
        partitions = service.ExecQuery(
            f"ASSOCIATORS OF {{Win32_LogicalDisk.DeviceID='{drive_letter}'}} WHERE ResultClass=Win32_DiskPartition")
        for partition in partitions:
            drives = service.ExecQuery(
                f"ASSOCIATORS OF {{Win32_DiskPartition.DeviceID='{partition.DeviceID}'}} WHERE ResultClass=Win32_DiskDrive")
            for drive in drives:
                # 判断是否为可移动媒体
                is_removable = drive.MediaType.lower().find("removable") >= 0
                drive_info[drive_letter] = {
                    "Caption": drive.Caption,
                    "MediaType": drive.MediaType,
                    "IsRemovable": is_removable,
                    "Status": drive.Status
                }
    return drive_info


def is_disk_usb_drive(part):
    is_removable = False
    drives = get_drive_info()
    disk = drives.get(part, {})
    print(f"is_disk_usb_drive disk={disk}, size={len(disk)}")
    if len(disk) > 0:
        is_removable = disk.get("IsRemovable")
        print(f"is_disk_usb_drive is_removable={is_removable}")

    return is_removable
