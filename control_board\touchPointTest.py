import copy
import logging

import math
import threading

import cv2
import time

from common.LogUtils import logger
from adb.AdbConnectDevice import adb_connect_device
from control_board.auto_test_m.ctr_card import ctr_card

import numpy as np

from control_board.inspire_robots.uart_client import inspire_client
from utils.SignalsManager import signals_manager
from vision.CameraManager import camera_manager


class touchPointTest:
    def __init__(self):
        self.case_number = ""
        self.command = ""
        self.finger_move_line_result = True
        self.slide_extra_info = {
            "synVel": 100,
            "synAcc": 0.1,
            "velEnd": 0
            }

    @staticmethod
    def display_center_point():
        adb_connect_device.adb_forward_send_data(action="display_center_point")

    @staticmethod
    def display_9_point():
        adb_connect_device.adb_forward_send_data(action="display_9_point")
    def display_checkerboard(self):
        adb_connect_device.adb_forward_send_data(action="showCheckerboard")

    def detact_checkerboard(self, frame):
        """
        检测图像中的棋盘格，并返回每个方格的中心点位置
        自动尝试不同尺寸的棋盘格检测

        参数:
            frame: 输入图像

        返回:
            points: 棋盘格中每个方格中心点的位置列表
        """
        points = []

        # 转换为灰度图像，提高角点检测的准确性
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # 定义可能的棋盘格尺寸列表（内角点数量）
        # 从小到大尝试不同尺寸
        possible_sizes = [
            (5, 5), (6, 5), (7, 5), (8, 5),
            (5, 6), (6, 6), (7, 6), (8, 6), (9, 6),
            (5, 7), (6, 7), (7, 7), (8, 7), (9, 7),
            (7, 8), (8, 8), (9, 8), (10, 8),
            (7, 9), (8, 9), (9, 9), (10, 9),
            (7, 10), (8, 10), (9, 10), (10, 10)
        ]

        # 尝试检测不同尺寸的棋盘格
        detected = False
        board_size = None
        corners = None

        for size in possible_sizes:
            ret, detected_corners = cv2.findChessboardCorners(gray, size, None)
            if ret:
                detected = True
                board_size = size
                corners = detected_corners
                break

        if detected:
            # 提高角点精度
            criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001)
            corners2 = cv2.cornerSubPix(gray, corners, (11, 11), (-1, -1), criteria)

            # 计算方格中心点
            # 棋盘格角点是按行优先排列的
            for i in range(board_size[1] - 1):
                for j in range(board_size[0] - 1):
                    # 计算四个角点的索引
                    idx1 = i * board_size[0] + j
                    idx2 = i * board_size[0] + (j + 1)
                    idx3 = (i + 1) * board_size[0] + j
                    idx4 = (i + 1) * board_size[0] + (j + 1)

                    # 计算方格中心点
                    cx = (corners2[idx1][0][0] + corners2[idx2][0][0] + corners2[idx3][0][0] + corners2[idx4][0][0]) / 4
                    cy = (corners2[idx1][0][1] + corners2[idx2][0][1] + corners2[idx3][0][1] + corners2[idx4][0][1]) / 4

                    points.append((cx, cy))

            # 可选：打印检测到的棋盘格尺寸
            # print(f"检测到棋盘格尺寸: {board_size}")

            # 可选：在图像上绘制检测到的角点和中心点
            # cv2.drawChessboardCorners(frame, board_size, corners2, ret)
            # for point in points:
            #     cv2.circle(frame, (int(point[0]), int(point[1])), 5, (0, 255, 0), -1)
        else:
            # 未检测到棋盘格
            pass
            # print("未检测到棋盘格")

        return points
    def get_checkerboard_coordinate(self):
        self.display_checkerboard()
        points =[]
        for i in range(30):
            time.sleep(2)
            frame = self.get_camera_image()
            points = self.detact_checkerboard(frame)
            if len(points)==0:
                # self.display_9_point()
                continue
            else:
                logging.info("get_9_points  length:{}, {}".format(len(points), points))
                break
        return points
    def get_9_points(self):
        self.display_9_point()
        points =[]
        for i in range(30):
            time.sleep(2)
            frame = self.get_camera_image()
            points = self.detact_point(frame)
            if len(points)==0:
                # self.display_9_point()
                continue
            else:
                logging.info("get_9_points  length:{}, {}".format(len(points), points))
                break
        return points

    def move_point(self, x, y, z=-4114.0, R_value=-24567.0, Z2_value=0):
        logger.info("move_point x:{},y:{},z:{},R_value:{},Z2_value:{}".format(x, y, z, R_value, Z2_value))
        X = 4
        Y = 3
        Z1 = 1
        Z2 = 2
        R = 5
        pos_d = {
            X: int(x),
            Y: int(y),
            Z1: int(z),
            Z2: int(Z2_value),
            R: int(R_value)
        }
        ctr_card.m_axis_trap_move(pos_d=pos_d)
        while ctr_card.m_axis_trap_move_result != 1:
            time.sleep(0.1)

    def touch_point(self, x, y, z):
        """
        触摸头弹出
        :param x:
        :param y:
        :param z:
        :return:
        """
        pass

    def get_camera_image(self):
        ret, frame = camera_manager.cap.read()  # 读取一帧视频
        # 保存图片
        image_path = "center_point.jpg"
        if ret:
            cv2.imwrite(image_path, frame)

        if ret:
            return frame
        else:
            return None

    def detact_point(self, frame,min_radius= 5,max_radius=50):

        # image = cv2.imread(image_path)
        # if image is None:
        #     print(f"无法读取图像: {image_path}")
        #     return None

        # 创建图像副本用于结果显示
        output = frame.copy()
        image = frame
        # 转换到HSV色彩空间
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

        # 定义红色的HSV范围（两个范围）
        lower_red1 = np.array([0, 100, 100])
        upper_red1 = np.array([10, 255, 255])
        lower_red2 = np.array([160, 100, 100])
        upper_red2 = np.array([180, 255, 255])

        # 创建红色的掩码
        mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
        mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
        red_mask = cv2.bitwise_or(mask1, mask2)

        # 保存原始掩码用于展示
        original_mask = red_mask.copy()

        # 应用形态学操作来改善掩码
        # 先腐蚀去除小噪点
        kernel = np.ones((3, 3), np.uint8)
        eroded_mask = cv2.erode(red_mask, kernel, iterations=1)

        # 再膨胀恢复主要形状并连接断裂部分
        kernel = np.ones((5, 5), np.uint8)
        dilated_mask = cv2.dilate(eroded_mask, kernel, iterations=2)

        # 闭运算填充内部小孔
        kernel = np.ones((7, 7), np.uint8)
        closed_mask = cv2.morphologyEx(dilated_mask, cv2.MORPH_CLOSE, kernel)

        # 应用高斯模糊以平滑边缘
        blurred_mask = cv2.GaussianBlur(closed_mask, (9, 9), 2)

        # 方法1：使用改进的HoughCircles参数
        # 降低param2参数使其对不完美的圆更宽容
        circles = cv2.HoughCircles(
            blurred_mask,
            cv2.HOUGH_GRADIENT,
            dp=1.5,  # 增加累加器分辨率比率
            minDist=20,  # 适当增加最小距离
            param1=50,  # Canny边缘检测的高阈值
            param2=25,  # 降低累加器阈值，对不规则圆更宽容
            minRadius=min_radius,
            maxRadius=max_radius,
           # maxRadius=0,
        )

        # 在原图上绘制检测到的圆
        result1 = output.copy()
        detected_circles = []  # 存储检测到的圆的信息

        if circles is not None:
            circles = np.uint16(np.around(circles))
            for i in circles[0, :]:
                detected_circles.append([int(i[0]), int(i[1])])

                # 绘制外圆
                cv2.circle(result1, (i[0], i[1]), i[2], (0, 255, 0), 2)
                # 绘制圆心
                cv2.circle(result1, (i[0], i[1]), 2, (0, 0, 255), 3)
                # 标记半径
                cv2.putText(result1, f"r={i[2]}", (i[0], i[1] - 10),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)

                # 保存图片
                image_path = "center_point_detect.jpg"
                cv2.imwrite(image_path, result1)
        point_list = detected_circles
        # 对点进行排序
        if len(point_list) == 9:  # 确保检测到9个点
            # 按y坐标排序
            point_list.sort(key=lambda x: x[1])

            # 将9个点分成3行
            rows = [
                point_list[0:3],  # 前3个点是第一行
                point_list[3:6],  # 中间3个点是第二行
                point_list[6:9]  # 最后3个点是第三行
            ]

            # 对每一行按x坐标排序
            for row in rows:
                row.sort(key=lambda x: x[0])

            # 重新组合所有点
            sorted_points = []
            for row in rows:
                sorted_points.extend(row)

            point_list = sorted_points
        return point_list

    def release_point(self, x, y, z):
        pass

    def draw_line_9points(self, points, ):
        logger.info(f"draw_line_9points input:{points}")

        def find_diagonals(points):
            distances = []

            for i in range(len(points)):
                for j in range(i + 1, len(points)):
                    x1, y1 = points[i]
                    x2, y2 = points[j]
                    dx = x1 - x2
                    dy = y1 - y2
                    distance = math.hypot(dx, dy)
                    distances.append((distance, points[i], points[j]))

            # 找到最大距离（对角线）的值
            max_distance = max(distances, key=lambda x: x[0])[0]

            # 找到所有距离等于最大距离的点对
            max_pairs = [(p1, p2) for d, p1, p2 in distances if abs(d - max_distance) < 1e-6]
            return max_pairs

        points = find_diagonals(points)
        start = points[0][0]
        self.move_point(start[0], start[1], 0)
        end = [list(points[0][1])]
        ctr_card.crd_move(end)
        # while ctr_card.crd_move_result!=0:
        #     time.sleep(0.1)
        self.wait_for_device(3)
        start = points[1][0]

        self.move_point(start[0], start[1], 0)
        end = [list(points[1][1])]
        ctr_card.crd_move(end)
        while ctr_card.crd_move_result != 0:
            time.sleep(0.1)
        logger.info("9点测试完成")

    def wait_for_device(self, timeout=3):
        start_time = int(time.time())
        while ctr_card.crd_move_result == 0 and time.time() - start_time < timeout:
            # print("ctr_card.crd_move_result:", ctr_card.crd_move_result)
            time.sleep(0.1)
        # print("end ctr_card.crd_move_result:", ctr_card.crd_move_result, time.time() - start_time, timeout,time.time() - start_time > timeout)

    import math
    def find_midpoints(self,points, axis='x'):

        mean_x = sum(x for x, y in points) / len(points)
        mean_y = sum(y for x, y in points) / len(points)

        # Step 2: Identify the center point (closest to the mean position)
        def distance(p1, p2):
            return math.hypot(p1[0] - p2[0], p1[1] - p2[1])

        mean_point = (mean_x, mean_y)
        center_point = min(points, key=lambda p: distance(p, mean_point))

        threshold = 10  # Adjust the threshold as needed

        if axis == 'x':
            potential_midpoints = [
                p for p in points
                if p != center_point and abs(p[0] - mean_x) <= threshold
            ]
            # Step 4: Sort by absolute difference in y-coordinate
            potential_midpoints.sort(key=lambda p: abs(p[1] - mean_y), reverse=True)
        elif axis == 'y':
            potential_midpoints = [
                p for p in points
                if p != center_point and abs(p[1] - mean_y) <= threshold
            ]
            potential_midpoints.sort(key=lambda p: abs(p[0] - mean_x), reverse=True)
        else:
            raise ValueError("Invalid axis specified. Must be 'x' or 'y'.")

        if len(potential_midpoints) < 2:
            if axis == 'x':
                threshold = max(abs(p[0] - mean_x) for p in points if p != center_point)
                potential_midpoints = [
                    p for p in points
                    if p != center_point and abs(p[0] - mean_x) <= threshold
                ]
                potential_midpoints.sort(key=lambda p: abs(p[1] - mean_y), reverse=True)
            elif axis == 'y':
                threshold = max(abs(p[1] - mean_y) for p in points if p != center_point)
                potential_midpoints = [
                    p for p in points
                    if p != center_point and abs(p[1] - mean_y) <= threshold
                ]
                potential_midpoints.sort(key=lambda p: abs(p[0] - mean_x), reverse=True)

        # Ensure we have at least two points
        if len(potential_midpoints) < 2:
            raise ValueError("Not enough points found to determine midpoints.")

        # Step 5: Select the two midpoints
        P1, P2 = potential_midpoints[:2]

        return P1, P2

    def touch_points_count(self, fingers):
        if fingers == 1:
            fingers_list = [3]
        elif fingers == 2:
            fingers_list = [3, 4]
        elif fingers == 3:
            fingers_list = [3, 4, 5]
        elif fingers == 4:
            fingers_list = [2, 3, 4, 5]
        elif fingers == 5:
            fingers_list = [2, 3, 4, 5,6]
        elif fingers == 6:
            fingers_list = [2, 3, 4, 5, 6,7]
        elif fingers == 7:
            fingers_list = [2, 3, 4, 5, 6, 7,8]
        elif fingers == 8:
            fingers_list = [2, 3, 4, 5, 6, 7,8,9]
        else:
            fingers_list = [2, 3, 4, 5]

        for f in fingers_list:
            inspire_client.move_to(f, 0)
        time.sleep(.5)
        adb_connect_device.adb_forward_send_data(action="start_touch_points")
        time.sleep(2)
        for f in fingers_list:
            inspire_client.move_to(f, 1400)
        time.sleep(3)
        adb_connect_device.adb_forward_send_data(action="end_touch_points")
        for f in fingers_list:
            inspire_client.move_to(f, 0)
        ctr_card.go_home()
    def finger2zero(self):
        for f in range(1,10):
            t_thread = threading.Thread(target=inspire_client.move_to, args=(f, 0))
            t_thread.start()
        time.sleep(3)
            # inspire_client.move_to(f, 0)


    def finger_move_line(self, start_point, end_point, fingers, tms=1):
        adb_connect_device.switch_color("#%02X%02X%02X" % (255, 255, 255))
        end_point = [int(i) for i in end_point]
        start_point = [int(i) for i in start_point]
        fingers = int(fingers)
        print(f"start_point:{start_point}")
        print(f"end_point:{end_point}")
        print(f"fingers tms:{fingers}  ;{tms}")
        self.move_point(start_point[0], start_point[1], z=-62369.0, R_value=12573.0, Z2_value=0)
        while ctr_card.m_axis_trap_move_result != 1:
            # print("ctr_card.m_axis_trap_move_result", ctr_card.m_axis_trap_move_result)
            time.sleep(0.1)
        ctr_card.setup_coordinate_system()
        if fingers == 1:
            fingers_list = [3]
        elif fingers == 2:
            fingers_list = [3, 4]
        elif fingers == 3:
            fingers_list = [3, 4, 5]
        elif fingers == 4:
            fingers_list = [2, 3, 4, 5]
        elif fingers == 5:
            fingers_list = [2, 3, 4, 5,6]
        elif fingers == 6:
            fingers_list = [2, 3, 4, 5, 6,7]
        elif fingers == 7:
            fingers_list = [2, 3, 4, 5, 6, 7,8]
        elif fingers == 8:
            fingers_list = [2, 3, 4, 5, 6, 7, 8,9]
        else:
            fingers_list = [2, 3, 4, 5]
        tms_origin = copy.deepcopy(tms)


        while tms > 0 and self.finger_move_line_result:
            end = [end_point]
            # 告诉app开始划线
            logger.info(f"finger_move_line process {tms_origin -tms} times")
            adb_connect_device.adb_forward_send_data(action="start_draw_lines",data=f"{fingers}")
            time.sleep(3)
            for f in fingers_list:
                t_thread = threading.Thread(target=inspire_client.move_to,args=(f, 1550))
                t_thread.start()
                # inspire_client.move_to(f, 1480)
            time.sleep(3)
            ctr_card.crd_move(end)
            self.wait_for_device(20)
            # 告诉app结束划线
            # time.sleep(1)
            adb_connect_device.adb_forward_send_data(action="end_draw_lines", data=f"{fingers}")
            time.sleep(3)
            for f in fingers_list:
                t_thread = threading.Thread(target=inspire_client.move_to, args=(f, 0))
                t_thread.start()
                # inspire_client.move_to(f, 0)
            time.sleep(2)
            start = [start_point]
            ctr_card.crd_move(start,extra = self.slide_extra_info)
            self.wait_for_device(20)
            tms -= 1


        ctr_card.go_home()
        if self.finger_move_line_result:
            signals_manager.step_execute_finish.emit(self.case_number, self.command, "PASS", "PASS")
        else:
            signals_manager.step_execute_finish.emit(self.case_number, self.command, "NG", f"Detect up event\n{tms} times left")


touch_card = touchPointTest()

if __name__ == '__main__':
    touch_card.display_9_point()
    time.sleep(1)
    frame = touch_card.get_camera_image()
    p = touch_card.detact_point(frame)
    # print(p)
    # touch_card.move_point(x,y,z)
    #
    # touch_card.move_point(0,0,0)
