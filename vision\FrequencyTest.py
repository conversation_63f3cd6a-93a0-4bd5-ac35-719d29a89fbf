# -*- coding: utf-8 -*-
"""
<AUTHOR> jid
@Date   : 2023/12/25 18:35
@Desc   : 
"""
import copy
import datetime
import json
import os
import queue
import subprocess
import time
from multiprocessing import Pool, freeze_support

import cv2
import numpy as np
from PyQt5.QtGui import QImage
from skimage.metrics import structural_similarity as ssim

from common.AppConfig import app_config
from common.LogUtils import logger
from vision.VisualDetectSignal import visual_detect_signal
from utils.SignalsManager import signals_manager
from vision.CameraConfig import threshold, camera_config
from vision.CameraManager import camera_manager
from vision.VideoDetectManager import video_detect_manager


class FrequencyTest:

    def __init__(self):
        self.is_ng = False
        self.is_start_one = False
        self.prev_frame_color = None
        self.prev_frame = None
        self.threshold = threshold
        self.diff = np.zeros((1280, 720), dtype=np.uint8)
        self.ng_count = 0
        self.start_index = 1
        self.start_record_index = False
        self.case_monitor_video = False
        visual_detect_signal.start_collect.connect(self.update_start_collect)
        visual_detect_signal.switch_durability.connect(self.update_start_durability)
        visual_detect_signal.function_test_clear_result.connect(self.clear_functionality_result)
        visual_detect_signal.start_brightness_test.connect(self.start_black_screen_value)
        self.ng_count_test = 0
        self.start_collect = True
        self.origin_brightness = -100
        self.start_time_black_screen = 0
        freeze_support()
        self.functionality_video_error = 0
        self.functionality_video_grainyScreen = 0
        self.functionality_video_blackScreen = 0
        self.current_files_path = None
        self.record_video_path = None
        self.save_path = os.path.join(app_config.work_folder, "config", "durability_data.json")
        self.all_count = 0
        self.img_prev_frame_color = None
        self.start_black_screen_index = False
        self.real_brightness = 0

    def update_start_collect(self, result):
        logger.info(f"update_start_collect result={result}")
        self.start_time_black_screen = time.time()
        self.start_collect = result
        if result:
            camera_manager.start_video(index=1)
        else:
            camera_manager.start_video(index=0)

    def update_start_durability(self, result):
        if result:
            self.start_collect = True
            self.clear_ng_count()
            visual_detect_signal.clear_ui_ng_count.emit()
        else:
            self.start_collect = False
            camera_config.up_power_status = True

    @staticmethod
    def collect_video_flicker_screen(video_path, start_total, end_total, split_count, save_path, th=10):
        logger.info(f"collect_video_flicker_screen video_path={video_path}, start_total={start_total}, "
                    f"end_total={end_total}, split_count={split_count}, save_path={save_path}, th={th}")
        cap = cv2.VideoCapture(video_path)
        end_des = end_total + split_count + 1
        now_dex = start_total
        # 设置初始帧和差异阈值
        prev_frame = None

        # 设置视频的当前帧位置
        cap.set(cv2.CAP_PROP_POS_FRAMES, now_dex)
        count = 0
        prev_frame_color = None

        while cap.isOpened() and end_des > now_dex:
            ret, frame = cap.read()
            if not ret:
                logger.info(f'collect_video_flicker_screen {str(video_path).split("/")[-1]} 读视频结束')
                break
            else:
                now_dex += 1

                # # 显示视频解析的图片
                # image = QImage(frame, frame.shape[1], frame.shape[0], QImage.Format_BGR888)
                # # 将 QImage 转换为 QPixmap
                # pixmap = QPixmap.fromImage(image)
                # signals_manager.collect_video_changed.emit(pixmap)

            # 转换为灰度图像
            gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            old_frame = copy.deepcopy(frame)
            # 计算相邻帧之间的差异
            if prev_frame is not None and prev_frame_color is not None:
                diff = cv2.absdiff(prev_frame, gray_frame)
                logger.info(f"collect_video_flicker_screen diff={diff}")
                # 计算差异值大于阈值的像素数量
                num_diff_pixels = np.sum(diff > threshold)
                logger.info(f"collect_video_flicker_screen num_diff_pixels={num_diff_pixels}")
                # 计算像素闪动的频率
                flicker_frequency = round(num_diff_pixels / (gray_frame.shape[0] * gray_frame.shape[1]), 3)
                video_detect_manager.flicker_values.append(flicker_frequency)
                # 判断是否有屏幕变化
                logger.info(f"collect_video_flicker_screen {flicker_frequency}, {camera_config.flicker_threshold}")
                if flicker_frequency > camera_config.flicker_threshold:
                    # 这里的阈值可以根据实际情况调整
                    count += 1
                    logger.info(f"collect_video_flicker_screen count={count}")
                    old_frame[diff > th] = [0, 0, 255]
                    img_path = f"{str(datetime.datetime.now()).replace(':', '_').replace('.', '_')}.jpg"
                    flicker_path = os.path.join(save_path, img_path)
                    combined_frame = np.hstack((frame, prev_frame_color, old_frame))
                    ret = cv2.imwrite(flicker_path, combined_frame)
                    video_detect_manager.flicker_errors.append(img_path)
                    logger.info(f"collect_video_flicker_screen flicker_path={flicker_path}, ret={ret}")

            # 更新前一帧
            prev_frame = gray_frame.copy()
            prev_frame_color = frame.copy()
            # 显示灰度图像和差异图像
            # cv2.imshow('Gray Frame', gray_frame)
            # cv2.imshow('Difference', frame)
            time.sleep(0.02)

        cap.release()
        return True

    def collect_video_grainy_screen(self, video_path, start_total, end_total, split_count, save_path):
        # 替换为你的摄像头设备编号或视频文件路径
        cap = cv2.VideoCapture(video_path)
        end_des = end_total + split_count + 1
        now_dex = start_total

        # 设置视频的当前帧位置
        cap.set(cv2.CAP_PROP_POS_FRAMES, now_dex)
        count = 0

        while cap.isOpened() and end_des > now_dex:
            ret, frame = cap.read()
            if not ret:
                logger.info(f'collect_video_grainy_screen {str(video_path).split("/")[-1]}读视频结束')
                break
            else:
                now_dex += 1

            # 计算相邻帧之间的差异
            similarity, error_img = self.texture_matching(frame)
            video_detect_manager.grainy_values.append(similarity)
            # 判断是否有屏幕变化
            logger.info(f"collect_video_grainy_screen {similarity}, {camera_config.grainy_threshold}")
            if similarity > camera_config.grainy_threshold:
                # 这里的阈值可以根据实际情况调整
                count += 1
                img_path = f"{str(datetime.datetime.now()).replace(':', '_').replace('.', '_')}.jpg"
                grainy_path = os.path.join(save_path, img_path)
                combined_frame = np.hstack((error_img, frame))
                ret = cv2.imwrite(grainy_path, combined_frame)
                video_detect_manager.grainy_errors.append(img_path)
                logger.info(f"collect_video_grainy_screen grainy_path={grainy_path}, ret={ret}")

            time.sleep(0.02)

        cap.release()
        return True

    def functionality_test_save_result(self, k, v):
        logger.info(f"functionality_test_save_result k={k}, v={v}")
        if not os.path.exists(self.save_path):
            with open(self.save_path, "w") as file:
                # json.dump({}, file, indent=4)
                data = json.dumps({}, indent=4)
                file.write(data)

        with open(self.save_path, "r") as file:
            existing_data = json.load(file)
        existing_data[k] = v
        logger.info(f"functionality_test_save_result existing_data={existing_data}")

        with open(self.save_path, "w") as file:
            # json.dump(existing_data, file, indent=4)
            data = json.dumps(existing_data, indent=4)
            file.write(data)

    def functionality_video_test(self, rect, video_path, start_total, end_total, split_count, brightness_origin,
                                 file_date, now_count, all_count, threshold_flicker, threshold_grainy, threshold_black,
                                 test_mode, base_path):
        print(f"functionality_video_test video_path={video_path}, start_total={start_total}, end_total={end_total},"
              f"split_count={split_count}, brightness_origin={brightness_origin}, file_date={file_date}, "
              f"now_count={now_count}, all_count={all_count}, threshold_flicker={threshold_flicker}, "
              f"threshold_grainy={threshold_grainy}, threshold_black={threshold_black}， test_mode={test_mode}, "
              f"base_path={base_path}")
        # 替换为你的摄像头设备编号或视频文件路径
        cap = cv2.VideoCapture(video_path)
        now_dex = start_total
        # 设置初始帧和差异阈值
        gray_frame = ""
        prev_frame = None

        # 设置视频的当前帧位置
        cap.set(cv2.CAP_PROP_POS_FRAMES, now_dex)
        count = 0
        grainy_screen_detect_value = []
        flicker_screen_detect_value = []
        black_screen_detect_value = []
        prev_frame_color = None
        functionality_result = {'flicker_screen': 0, 'grainy_screen': 0, 'black_screen': 0}

        save_ng_flicker_img_path = os.path.join(base_path, "ng_flicker_img", file_date)
        save_ng_grainy_img_path = os.path.join(base_path, "ng_grainy_img", file_date)
        save_ng_black_img_path = os.path.join(base_path, "ng_black_img", file_date)

        if not os.path.exists(save_ng_black_img_path):
            os.makedirs(save_ng_black_img_path)

        if not os.path.exists(save_ng_flicker_img_path):
            os.makedirs(save_ng_flicker_img_path)

        if not os.path.exists(save_ng_grainy_img_path):
            os.makedirs(save_ng_grainy_img_path)

        old_frame = None
        while cap.isOpened():
            ret, frame = cap.read()
            if not ret:
                print(f'functionality_video_test {str(video_path).split("/")[-1]}读视频结束')
                break
            else:
                now_dex += 1

            # 转换为灰度图像
            if rect is None:
                return
            rects = (rect.x(), rect.y(), rect.width(), rect.height())
            frame_cropped = frame[rects[1]:rects[1] + rects[3], rects[0]:rects[0] + rects[2]]
            gray_frame = cv2.cvtColor(frame_cropped, cv2.COLOR_BGR2GRAY)
            old_frame = copy.deepcopy(frame)
            # 计算相邻帧之间的差异
            if prev_frame is not None and prev_frame_color is not None:
                try:
                    if test_mode == 1:
                        # 闪屏检测模式
                        diff = cv2.absdiff(prev_frame, gray_frame)
                        # 计算差异值大于阈值的像素数量
                        num_diff_pixels = np.sum(diff > threshold)
                        # 计算像素闪动的频率
                        flicker_frequency = num_diff_pixels / (gray_frame.shape[0] * gray_frame.shape[1])
                        flicker_frequency = round(flicker_frequency, 3)
                        # 判断是否有屏幕变化，这里的阈值可以根据实际情况调整
                        if flicker_frequency > threshold_flicker:
                            count += 1
                            functionality_result['flicker_screen'] = functionality_result['flicker_screen'] + 1
                            reason = f"闪屏检测值：{flicker_frequency} 大于标定阈值：{threshold_flicker}"
                            functionality_result.update({"flicker_screen_reason": reason})
                            old_frame[diff > threshold] = [0, 0, 255]
                            img_path = f"{str(datetime.datetime.now()).replace(':', '_').replace('.', '_')}.jpg"
                            last_path = os.path.join(base_path, "ng_flicker_img", file_date, img_path)
                            print(f"functionality_video_test mode={test_mode}, last_path={last_path}")
                            combined_frame = np.hstack((frame, prev_frame_color, old_frame))
                            ret = cv2.imwrite(last_path, combined_frame)
                            print(f"functionality_video_test mode={test_mode}, flicker_screen_save_ret={ret}")
                            print(f"functionality_video_test flicker_frequency={flicker_frequency}, "
                                  f"threshold_flicker={threshold_flicker}")
                        flicker_screen_detect_value.append(f"{flicker_frequency}")
                    elif test_mode == 2:
                        # 花屏检测模式
                        similarity, error_img = self.texture_matching(frame)
                        # 这里的阈值可以根据实际情况调整
                        if similarity > threshold_grainy:
                            count += 1
                            functionality_result['grainy_screen'] = functionality_result['grainy_screen'] + 1
                            reason = f"花屏检测值：{similarity} 大于标定阈值：{threshold_grainy}"
                            functionality_result.update({"grainy_screen_reason": reason})
                            print(f"functionality_video_test similarity={similarity}, "
                                  f"threshold_grainy={threshold_grainy}")
                            img_path = f"{str(datetime.datetime.now()).replace(':', '_').replace('.', '_')}.jpg"
                            last_path = os.path.join(base_path, "ng_grainy_img", file_date, img_path)
                            print(f"functionality_video_test mode={test_mode}, last_path={last_path}")
                            combined_frame = np.hstack((error_img, frame))
                            ret = cv2.imwrite(last_path, combined_frame)
                            print(f"functionality_video_test mode={test_mode}, grainy_screen_save_ret={ret}")
                        grainy_screen_detect_value.append(f"{similarity}")
                    elif test_mode == 0:
                        diff = cv2.absdiff(prev_frame, gray_frame)
                        # 计算差异值大于阈值的像素数量
                        num_diff_pixels = np.sum(diff > threshold)
                        # 计算像素闪动的频率
                        flicker_frequency = num_diff_pixels / (gray_frame.shape[0] * gray_frame.shape[1])
                        flicker_frequency = round(flicker_frequency, 3)
                        print(f"functionality_video_test flicker_frequency={flicker_frequency}, "
                              f"threshold_flicker={threshold_flicker}")
                        # 判断是否有屏幕变化 这里的阈值可以根据实际情况调整
                        if flicker_frequency > threshold_flicker:
                            count += 1
                            functionality_result['flicker_screen'] = functionality_result['flicker_screen'] + 1
                            reason = f"闪屏检测值：{flicker_frequency} 大于标定阈值：{threshold_flicker}"
                            functionality_result.update({"flicker_screen_reason": reason})
                            old_frame[diff > threshold] = [0, 0, 255]
                            img_path = f"{str(datetime.datetime.now()).replace(':', '_').replace('.', '_')}.jpg"
                            last_path = os.path.join(base_path, "ng_flicker_img", file_date, img_path)
                            combined_frame = np.hstack((frame, prev_frame_color, old_frame))
                            ret = cv2.imwrite(last_path, combined_frame)
                            print(f"functionality_video_test mode={test_mode}, flicker_screen_save_ret={ret}")
                        flicker_screen_detect_value.append(f"{flicker_frequency}")

                        similarity, error_img = self.texture_matching(frame)
                        print(f"functionality_video_test similarity={similarity}, threshold_grainy={threshold_grainy}")
                        # 这里的阈值可以根据实际情况调整
                        if similarity > threshold_grainy:
                            count += 1
                            functionality_result['grainy_screen'] = functionality_result['grainy_screen'] + 1
                            reason = f"花屏检测值：{similarity} 大于标定阈值：{threshold_grainy}"
                            functionality_result.update({"grainy_screen_reason": reason})
                            print(f"functionality_video_test mode={test_mode}, {similarity}|{threshold_grainy}")
                            img_path = f"{str(datetime.datetime.now()).replace(':', '_').replace('.', '_')}.jpg"
                            last_path = os.path.join(base_path, "ng_grainy_img", file_date, img_path)
                            print(f"functionality_video_test mode={test_mode}, last_path={last_path}")
                            combined_frame = np.hstack((error_img, frame))
                            ret = cv2.imwrite(last_path, combined_frame)
                            print(f"functionality_video_test mode={test_mode}, grainy_screen_save_ret={ret}")
                        grainy_screen_detect_value.append(f"{similarity}")
                except Exception as e:
                    print(f"functionality_video_test exception: {str(e.args)}")
                    if test_mode == 0:
                        functionality_result['flicker_screen'] = functionality_result['flicker_screen'] + 1
                        functionality_result['grainy_screen'] = functionality_result['grainy_screen'] + 1
                        functionality_result.update({"flicker_screen_reason": "视频尺寸解析异常"})
                        functionality_result.update({"grainy_screen_reason": "视频尺寸解析异常"})
                        img_path = f"{str(datetime.datetime.now()).replace(':', '_').replace('.', '_')}.jpg"
                        last_path = os.path.join(base_path, "ng_flicker_img", file_date, img_path)
                        combined_frame = np.hstack((frame, prev_frame_color, old_frame))
                        ret = cv2.imwrite(last_path, combined_frame)
                        print(f"functionality_video_test mode={test_mode}, flicker_screen_save_ret={ret}")
                        last_path = os.path.join(base_path, "ng_grainy_img", file_date, img_path)
                        combined_frame = np.hstack((frame, prev_frame_color, old_frame))
                        ret = cv2.imwrite(last_path, combined_frame)
                        print(f"functionality_video_test mode={test_mode}, grainy_screen_save_ret={ret}")
                    elif test_mode == 1:
                        functionality_result['flicker_screen'] = functionality_result['flicker_screen'] + 1
                        functionality_result.update({"flicker_screen_reason": "视频尺寸解析异常"})
                        img_path = f"{str(datetime.datetime.now()).replace(':', '_').replace('.', '_')}.jpg"
                        last_path = os.path.join(base_path, "ng_flicker_img", file_date, img_path)
                        combined_frame = np.hstack((frame, prev_frame_color, old_frame))
                        ret = cv2.imwrite(last_path, combined_frame)
                        print(f"functionality_video_test mode={test_mode}, flicker_screen_save_ret={ret}")
                    elif test_mode == 2:
                        functionality_result['grainy_screen'] = functionality_result['grainy_screen'] + 1
                        functionality_result.update({"grainy_screen_reason": "视频尺寸解析异常"})
                        img_path = f"{str(datetime.datetime.now()).replace(':', '_').replace('.', '_')}.jpg"
                        last_path = os.path.join(base_path, "ng_grainy_img", file_date, img_path)
                        combined_frame = np.hstack((frame, prev_frame_color, old_frame))
                        ret = cv2.imwrite(last_path, combined_frame)
                        print(f"functionality_video_test mode={test_mode}, grainy_screen_save_ret={ret}")

                    print(f"functionality_video_test functionality_result={functionality_result}")

            # 更新前一帧
            prev_frame = gray_frame.copy()
            prev_frame_color = frame.copy()

        brightness = int(gray_frame.mean())
        logger.info(f"functionality_video_test mode={test_mode}, brightness_origin={brightness_origin}, "
                    f"threshold_black={threshold_black}, brightness={brightness}")
        if brightness < brightness_origin - threshold_black:
            img_path = f"{str(datetime.datetime.now()).replace(':', '_').replace('.', '_')}.jpg"
            black_screen_save_path = os.path.join(base_path, "ng_black_img", file_date, img_path)
            if old_frame is not None:
                ret = cv2.imwrite(black_screen_save_path, old_frame)
                logger.info(f"functionality_video_test mode={test_mode}, black_screen_save_ret={ret}")
            functionality_result['black_screen'] = functionality_result['black_screen'] + 1
            reason = f"检测亮度：{brightness} 小于标定亮度：{brightness_origin} - {threshold_black}"
            functionality_result.update({"black_screen_reason": reason})
        black_screen_detect_value.append(f"标定亮度：{brightness_origin} - 检测亮度：{brightness}")
        cap.release()
        functionality_result.update({"grainy_screen_detect_value": grainy_screen_detect_value})
        functionality_result.update({"flicker_screen_detect_value": flicker_screen_detect_value})
        functionality_result.update({"black_screen_detect_value": black_screen_detect_value})
        logger.info(f"functionality_video_test mode={test_mode}, functionality_result={functionality_result}")
        self.functionality_test_save_result(now_count, functionality_result)
        return functionality_result

    @staticmethod
    def split_number(number, parts):
        # 计算每份的基础值
        base_value = number // parts
        # 计算余数
        remainder = number % parts
        # 初始化结果列表
        result = []
        # 计算每份的起始值
        start_value = 0

        for i in range(parts):
            # 计算每份的结束值
            end_value = start_value + base_value + (1 if i < remainder else 0)
            # 将起始值和结束值添加到结果列表
            result.append((start_value, end_value))
            # 更新起始值为当前结束值，用于下一份的计算
            start_value = end_value

        return result

    @staticmethod
    def compress_video(input_video_path, crf=23):
        logger.info(f"compress_video input_video_path={input_video_path}, crf={crf}")
        output_video_path = input_video_path.replace('.mp4', 'ys.mp4')
        ffmpeg_pah = os.path.join(os.getcwd(), "ffmpeg", "bin", "ffmpeg.exe")
        ffmpeg_cmd = [
            ffmpeg_pah,
            '-i', input_video_path,
            '-c:v', 'libxvid',  # 使用 libxvid 编码器来处理 MP4V 格式
            '-q:v', str(crf),  # 设置视频质量
            '-c:a', 'aac',
            '-strict', 'experimental',
            '-b:a', '128k',
            output_video_path
        ]

        try:
            subprocess.run(ffmpeg_cmd, check=True)
            logger.info("compress_video 视频压缩完成！")
            os.remove(input_video_path)
            logger.info('compress_video 原视频删除完成')
        except Exception as e:
            logger.error(f"compress_video 视频压缩失败: {str(e.args)}")

    @staticmethod
    def calculate_gabor_feature(image, frequency, theta, kernel_size=(21, 21)):
        logger.info(f"calculate_gabor_feature frequency={frequency}, theta={theta}, kernel_size={kernel_size}")
        # 创建 Gabor 滤波器
        gabor_kernel = cv2.getGaborKernel(kernel_size, sigma=4.0, theta=theta, lambd=10.0, gamma=0.5, psi=0,
                                          ktype=cv2.CV_32F)
        # 应用 Gabor 滤波器到图像
        gabor_filtered = cv2.filter2D(image, cv2.CV_8UC3, gabor_kernel)

        return gabor_filtered

    def texture_matching_bak(self, template_path, frequency=0.6, theta=0):
        """
        花屏检测. -滤波 -未使用
        Parameters
        ----------
        template_path :
        frequency :
        theta :

        Returns
        -------

        """
        start_time = time.time()
        template = template_path
        target = cv2.imread('./MainWindow/durability_test/setting/grainy_screen.jpg', cv2.IMREAD_COLOR)

        target_resized = cv2.resize(target, (template.shape[1], template.shape[0]))

        gabor_template = self.calculate_gabor_feature(template, frequency, theta)
        gabor_target = self.calculate_gabor_feature(target_resized, frequency, theta)

        gabor_template_gray = cv2.cvtColor(gabor_template, cv2.COLOR_BGR2GRAY)
        gabor_target_gray = cv2.cvtColor(gabor_target, cv2.COLOR_BGR2GRAY)

        similarity_index, _ = ssim(gabor_template_gray, gabor_target_gray, full=True)
        print('end_time', time.time() - start_time)
        return similarity_index

    @staticmethod
    def texture_matching(img):
        fast = cv2.FastFeatureDetector_create()
        key_points = fast.detect(img, None)
        img2 = cv2.drawKeypoints(img, key_points, None, color=(255, 0, 0))
        return len(key_points), img2

    def start_record(self, frame_queue: queue.Queue, file_date):
        self.start_record_index = True
        fourcc = cv2.VideoWriter_fourcc(*"mp4v")
        is_one_start = True
        video_writer = None
        logger.info(f'start_record start_record_index={self.start_record_index}, is_one_start={is_one_start}')
        while self.start_record_index:
            if not frame_queue.empty():
                frame = frame_queue.get()
                frame_queue.queue.clear()

                if is_one_start:
                    height, width, layers = frame.shape
                    self.current_files_path = os.path.join(camera_config.get_video_path(), file_date)
                    logger.info(f"start_record current_files_path={self.current_files_path}")
                    if not os.path.exists(self.current_files_path):
                        os.makedirs(self.current_files_path)
                    mp4_name = f"{datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.mp4"
                    self.record_video_path = os.path.join(camera_config.get_video_path(), file_date, mp4_name)

                    video_writer = cv2.VideoWriter(self.record_video_path, fourcc, 9, (width, height))
                    is_one_start = False

                current_time = datetime.datetime.now()
                formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
                # 设置字体、颜色和大小
                font = cv2.FONT_HERSHEY_SIMPLEX
                color = (255, 255, 255)
                font_size = 0.5
                font_thickness = 1
                cv2.putText(frame, formatted_time, (10, 30), font, font_size, color, font_thickness, cv2.LINE_AA)
                # 保存视频
                video_writer.write(frame)
            time.sleep(0.1)

        if video_writer is not None:
            video_writer.release()

    def start_case_monitor_video(self, case_monitor_video_path, frame_queue: queue.Queue):
        logger.info(f"start_case_monitor_video case_monitor_video_path={case_monitor_video_path}")
        self.case_monitor_video = True
        camera_manager.monitor_flag = True
        fourcc = cv2.VideoWriter_fourcc(*"XVID")
        is_one_start = True
        video_writer = None
        logger.info(f'start_case_monitor_video case_monitor_video={self.case_monitor_video}')
        while self.case_monitor_video:
            if not frame_queue.empty():
                frame = frame_queue.get()
                frame_queue.queue.clear()

                if is_one_start:
                    height, width, layers = frame.shape
                    mp4_name = f"{datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.mp4"
                    monitor_video_path = os.path.join(case_monitor_video_path, mp4_name)
                    logger.info(f"start_case_monitor_video monitor_video_path={monitor_video_path}")
                    video_writer = cv2.VideoWriter(monitor_video_path, fourcc, 9, (width, height))
                    is_one_start = False

                current_time = datetime.datetime.now()
                formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
                # 设置字体、颜色和大小
                font = cv2.FONT_HERSHEY_SIMPLEX
                color = (255, 255, 255)
                font_size = 0.5
                font_thickness = 1
                cv2.putText(frame, formatted_time, (10, 30), font, font_size, color, font_thickness, cv2.LINE_AA)
                # 保存视频
                video_writer.write(frame)
            time.sleep(0.1)

        if video_writer is not None:
            video_writer.release()

    def stop_case_monitor_video(self):
        logger.info("stop_case_monitor_video")
        self.case_monitor_video = False
        camera_manager.monitor_flag = False

    def stop_test(self):
        self.start_index = 0

    def start_video_test_flicker(self, paths):
        logger.info(f"start_video_test_flicker paths={paths}")
        video_detect_manager.flicker_errors.clear()
        video_detect_manager.grainy_errors.clear()
        video_detect_manager.flicker_values.clear()
        video_detect_manager.grainy_values.clear()
        save_path = camera_config.get_ng_flicker_img_path()
        # spilt_count = 7  # 视频切分份数
        # with Pool(processes=7) as pool:
        #     for path in paths:
        #         cap = cv2.VideoCapture(path)
        #         total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        #         cap.release()
        #         result = self.split_number(total_frames, spilt_count)
        #         results = []
        #         for i in result:
        #             result = pool.apply_async(self.collect_video_flicker_screen,
        #                                       (path, i[0], i[1], spilt_count, save_path))
        #             results.append(result)
        #         for res in results:
        #             res.get()
        #
        #     logger.info(f"start_video_test_flicker flicker_errors={video_detect_manager.flicker_errors}")
        #     if len(video_detect_manager.flicker_errors) > 0:
        #         signals_manager.video_test_finish.emit("视频检测完成(注意：视频文件不能有中文)\n检测到闪屏异常")
        #     else:
        #         signals_manager.video_test_finish.emit("视频检测完成(注意：视频文件不能有中文)\n未检测到闪屏异常")

        spilt_count = 1  # 视频切分份数
        for path in paths:
            cap = cv2.VideoCapture(path)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            cap.release()
            result = self.split_number(total_frames, spilt_count)
            for i in result:
                self.collect_video_flicker_screen(path, i[0], i[1], spilt_count, save_path)

            logger.info(f"start_video_test_flicker flicker_errors={video_detect_manager.flicker_errors}")
            if len(video_detect_manager.flicker_errors) > 0:
                signals_manager.video_test_finish.emit("闪屏测试", "视频检测完成（注意：视频文件不能有中文）\n\n检测到闪屏异常")
            else:
                signals_manager.video_test_finish.emit("闪屏测试", "视频检测完成（注意：视频文件不能有中文）\n\n未检测到闪屏异常")

    def start_video_test_grainy(self, paths):
        logger.info(f"start_video_test_grainy paths={paths}")
        video_detect_manager.flicker_errors.clear()
        video_detect_manager.grainy_errors.clear()
        video_detect_manager.flicker_values.clear()
        video_detect_manager.grainy_values.clear()
        save_path = camera_config.get_ng_grainy_img_path()
        # spilt_count = 7  # 视频切分份数
        # with Pool(processes=7) as pool:
        #     for path in paths:
        #         cap = cv2.VideoCapture(path)
        #         total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        #         cap.release()
        #         result = self.split_number(total_frames, spilt_count)
        #         results = []
        #         for i in result:
        #             result = pool.apply_async(self.collect_video_grainy_screen,
        #                                       (path, i[0], i[1], spilt_count, save_path))
        #             results.append(result)
        #         for res in results:
        #             res.get()

        spilt_count = 1  # 视频切分份数
        for path in paths:
            cap = cv2.VideoCapture(path)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            cap.release()
            result = self.split_number(total_frames, spilt_count)
            for i in result:
                self.collect_video_grainy_screen(path, i[0], i[1], spilt_count, save_path)

            logger.info(f"start_video_test_grainy grainy_errors={video_detect_manager.grainy_errors}")
            if len(video_detect_manager.grainy_errors) > 0:
                signals_manager.video_test_finish.emit("花屏测试", "视频检测完成（注意：视频文件不能有中文）\n\n检测到花屏异常")
            else:
                signals_manager.video_test_finish.emit("花屏测试", "视频检测完成（注意：视频文件不能有中文）\n\n未检测到花屏异常")

    def start_functionality_video_test(self, paths, dl, file_date, now_count, all_count, threshold_flicker,
                                       threshold_grainy, threshold_black, test_mode, base_path):
        # 视频切分份数
        spilt_count = 1
        with Pool(processes=1) as pool:
            for path in paths:
                cap = cv2.VideoCapture(path)
                total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                cap.release()
                result = self.split_number(total_frames, spilt_count)
                results = []
                #logger.info((f'start_functionality_video_test result_list={result}')
                for i in result:
                    result_grainy_screen = pool.apply_async(self.functionality_video_test, (
                        dl.rect, path, i[0], i[1], spilt_count, dl.brightness_origin, file_date, now_count, all_count,
                        threshold_flicker, threshold_grainy, threshold_black, test_mode, base_path))
                    results.append(result_grainy_screen)

                try:
                    for res in results:
                        res.get()
                except Exception as e:
                    logger.info(f'start_functionality_video_test exception: {str(e.args)}')

    @staticmethod
    def convert_cv_to_qimage(frame):
        height, width, channel = frame.shape
        bytes_per_line = 3 * width
        frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        q_image = QImage(bytes(frame.data), width, height, bytes_per_line, QImage.Format_RGB888)
        return q_image

    def clear_ng_count(self):
        self.ng_count = 0
        self.ng_count_test = 0

    def get_functionality_result(self):
        try:
            logger.info(f'get_functionality_result save_path={self.save_path}, all_count={self.all_count}')
            with open(self.save_path, "r") as file:
                start_time = time.time()
                check_count = 0
                while time.time() - start_time <= 60 * 10:
                    # 检测视频结果最多允许检测60*10s
                    file.seek(0)
                    # 优化：减少轮询间隔从0.5秒到0.2秒，提高响应速度
                    time.sleep(0.2)
                    check_count += 1
                    try:
                        existing_data = json.load(file)
                        data_keys_count = len(list(existing_data.keys()))
                        # 每10次检查打印一次详细日志，减少日志量
                        if check_count % 10 == 1:
                            logger.info(f'get_functionality_result existing_data keys count={data_keys_count}, target={self.all_count}, check_count={check_count}')
                        if data_keys_count == self.all_count:
                            logger.info(f'get_functionality_result completed: existing_data={existing_data}')
                            return existing_data
                    except json.JSONDecodeError as json_e:
                        # 文件可能正在写入，JSON格式暂时不完整，继续等待
                        if check_count % 25 == 1:  # 每5秒打印一次JSON错误
                            logger.warning(f'get_functionality_result JSON decode error (check #{check_count}): {str(json_e)}')
                        continue
                logger.warning(f'get_functionality_result timeout after {60 * 10}s, final check_count={check_count}')
                return {}
        except FileNotFoundError:
            logger.error(f"get_functionality_result file not found: {self.save_path}")
            return None
        except Exception as e:
            logger.error("get_functionality_result exception: {}".format(str(e.args)))
            return None

    def clear_functionality_result(self):
        logger.info("clear_functionality_result")
        with open(self.save_path, "w") as file:
            # json.dump({}, file, indent=4)
            data = json.dumps({}, indent=4)
            file.write(data)

    def start_black_screen_value(self):
        self.start_black_screen_index = True

    def get_black_screen_value(self):
        return self.real_brightness


frequency_test: FrequencyTest = FrequencyTest()
