#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DBC CAN 消息使用示例
展示如何在测试步骤中使用 DBC 格式的 CAN 消息
"""

import json

# 示例1: 基本的 DBC CAN 测试步骤
def create_dbc_can_step_example():
    """创建 DBC CAN 测试步骤示例"""
    
    # DBC 消息定义
    dbc_data = {
        "messages": {
            "Engine_Data": {
                "id": 256,  # 0x100
                "dlc": 8,
                "signals": [
                    {
                        "name": "Engine_Speed",
                        "start_bit": 0,
                        "length": 16,
                        "factor": 0.25,
                        "offset": 0,
                        "current_value": 2500.75
                    },
                    {
                        "name": "Engine_Torque", 
                        "start_bit": 16,
                        "length": 16,
                        "factor": 0.1,
                        "offset": -500,
                        "current_value": 250.5
                    },
                    {
                        "name": "Throttle_Position",
                        "start_bit": 32,
                        "length": 8,
                        "factor": 0.4,
                        "offset": 0,
                        "current_value": 65.2
                    }
                ]
            }
        }
    }
    
    # 测试步骤定义
    step = {
        "case_number": "DBC_TEST_001",
        "command": "DBC_Engine_Data_Send",
        "dbc_data": dbc_data,  # DBC 消息定义
        "message_name": "Engine_Data",  # 要发送的消息名称
        "signal_updates": {  # 可选：更新特定信号的值
            "Engine_Speed": 3000.0,
            "Engine_Torque": 300.0
        },
        "channel": 0,  # CAN 通道
        "can_type": "can",  # CAN 类型
        "expect": ["13 27 51 1D A3 00 00 00"],  # 期望的响应数据
        "recv_id": "100"  # 期望的响应 ID
    }
    
    return step

# 示例2: 多消息 DBC 测试
def create_multi_message_dbc_example():
    """创建多消息 DBC 测试示例"""
    
    dbc_data = {
        "messages": {
            "Vehicle_Speed": {
                "id": 512,  # 0x200
                "dlc": 8,
                "signals": [
                    {
                        "name": "Vehicle_Speed",
                        "start_bit": 0,
                        "length": 16,
                        "factor": 0.0625,
                        "offset": 0,
                        "current_value": 80.5
                    },
                    {
                        "name": "Wheel_Speed_FL",
                        "start_bit": 16,
                        "length": 12,
                        "factor": 0.1,
                        "offset": 0,
                        "current_value": 81.2
                    }
                ]
            },
            "Body_Control": {
                "id": 768,  # 0x300
                "dlc": 8,
                "signals": [
                    {
                        "name": "Door_Status",
                        "start_bit": 0,
                        "length": 4,
                        "factor": 1,
                        "offset": 0,
                        "current_value": 5
                    },
                    {
                        "name": "Light_Status",
                        "start_bit": 8,
                        "length": 8,
                        "factor": 1,
                        "offset": 0,
                        "current_value": 12
                    }
                ]
            }
        }
    }
    
    # 测试步骤1: 发送车速信息
    step1 = {
        "case_number": "DBC_TEST_002",
        "command": "DBC_Vehicle_Speed_Send",
        "dbc_data": dbc_data,
        "message_name": "Vehicle_Speed",
        "signal_updates": {
            "Vehicle_Speed": 120.0,  # 更新车速为120 km/h
            "Wheel_Speed_FL": 122.5
        },
        "channel": 0,
        "can_type": "can"
    }
    
    # 测试步骤2: 发送车身控制信息
    step2 = {
        "case_number": "DBC_TEST_003", 
        "command": "DBC_Body_Control_Send",
        "dbc_data": dbc_data,
        "message_name": "Body_Control",
        "signal_updates": {
            "Door_Status": 15,  # 所有门打开
            "Light_Status": 255  # 所有灯打开
        },
        "channel": 0,
        "can_type": "can"
    }
    
    return [step1, step2]

# 示例3: CANFD 消息示例
def create_canfd_dbc_example():
    """创建 CANFD DBC 测试示例"""
    
    dbc_data = {
        "messages": {
            "CANFD_Extended_Data": {
                "id": 1024,  # 0x400
                "dlc": 64,   # CANFD 支持更大的数据长度
                "signals": [
                    {
                        "name": "Sensor_Data_1",
                        "start_bit": 0,
                        "length": 32,
                        "factor": 0.001,
                        "offset": 0,
                        "current_value": 123456.789
                    },
                    {
                        "name": "Sensor_Data_2",
                        "start_bit": 32,
                        "length": 32,
                        "factor": 0.01,
                        "offset": -1000,
                        "current_value": 987.65
                    },
                    {
                        "name": "Status_Flags",
                        "start_bit": 64,
                        "length": 16,
                        "factor": 1,
                        "offset": 0,
                        "current_value": 0xABCD
                    }
                ]
            }
        }
    }
    
    step = {
        "case_number": "DBC_TEST_004",
        "command": "DBC_CANFD_Extended_Send",
        "dbc_data": dbc_data,
        "message_name": "CANFD_Extended_Data",
        "channel": 0,
        "can_type": "canfd",  # 指定为 CANFD
        "signal_updates": {
            "Sensor_Data_1": 999999.999,
            "Status_Flags": 0x1234
        }
    }
    
    return step

# 示例4: 与传统字节格式的对比
def compare_traditional_vs_dbc():
    """对比传统字节格式和 DBC 格式"""
    
    print("=== 传统字节格式 vs DBC 格式对比 ===\n")
    
    # 传统格式
    traditional_step = {
        "case_number": "TRADITIONAL_001",
        "command": "Traditional_CAN_Send",
        "id": "100",  # 十六进制字符串
        "msg": "13 27 51 1D A3 00 00 00",  # 手动计算的字节
        "channel": 0,
        "can_type": "can",
        "expect": ["13 27 51 1D A3 00 00 00"]
    }
    
    # DBC 格式
    dbc_step = create_dbc_can_step_example()
    
    print("传统格式:")
    print(json.dumps(traditional_step, indent=2, ensure_ascii=False))
    
    print("\nDBC 格式:")
    print(json.dumps(dbc_step, indent=2, ensure_ascii=False))
    
    print("\n优势对比:")
    print("传统格式:")
    print("  ✓ 简单直接")
    print("  ✗ 需要手动计算字节")
    print("  ✗ 不易理解和维护")
    print("  ✗ 容易出错")
    
    print("\nDBC 格式:")
    print("  ✓ 语义清晰，易于理解")
    print("  ✓ 自动处理编码/解码")
    print("  ✓ 支持信号级别的操作")
    print("  ✓ 减少人为错误")
    print("  ✓ 便于维护和调试")

if __name__ == "__main__":
    print("DBC CAN 消息使用示例\n")
    
    # 示例1
    print("1. 基本 DBC CAN 测试步骤:")
    step1 = create_dbc_can_step_example()
    print(json.dumps(step1, indent=2, ensure_ascii=False))
    
    # 示例2
    print("\n2. 多消息 DBC 测试:")
    steps2 = create_multi_message_dbc_example()
    for i, step in enumerate(steps2, 1):
        print(f"\n步骤 {i}:")
        print(json.dumps(step, indent=2, ensure_ascii=False))
    
    # 示例3
    print("\n3. CANFD DBC 测试:")
    step3 = create_canfd_dbc_example()
    print(json.dumps(step3, indent=2, ensure_ascii=False))
    
    # 对比
    print("\n4. 格式对比:")
    compare_traditional_vs_dbc()
