import os
import shutil
from datetime import datetime

from common.LogUtils import logger


def get_folder_date(folder_path, use_creation_time=False):
    """获取文件夹的日期（修改时间或创建时间）"""
    try:
        if use_creation_time:
            return os.path.getctime(folder_path)
        else:
            return os.path.getmtime(folder_path)
    except Exception as e:
        logger.error(f"get_folder_date 无法获取文件夹 {folder_path} 的日期: {e}")
        return 0


def cleanup_folders(base_dir, keep_count=15, use_creation_time=True, dry_run=True):
    """
    清理文件夹，保留最新的 keep_count 个文件夹
    
    参数:
    - base_dir: 要清理的基础目录
    - keep_count: 要保留的文件夹数量
    - use_creation_time: 是否使用创建时间而非修改时间
    - dry_run: 是否只显示操作而不实际删除
    """
    # 获取所有子文件夹及其日期
    folders = []
    for item in os.listdir(base_dir):
        item_path = os.path.join(base_dir, item)
        if os.path.isdir(item_path):
            folder_date = get_folder_date(item_path, use_creation_time)
            if folder_date:
                folders.append((item_path, folder_date))

    # 按日期排序（从旧到新）
    folders.sort(key=lambda x: x[1])

    # 计算要删除的文件夹
    folders_to_delete = folders[:max(0, len(folders) - keep_count)]

    logger.info(f"cleanup_folders 基础目录: {base_dir}")
    logger.info(f"cleanup_folders 找到 {len(folders)} 个子文件夹")
    logger.info(f"cleanup_folders 将保留最新的 {keep_count} 个文件夹")

    if not folders_to_delete:
        logger.info(f"cleanup_folders 没有需要删除的文件夹")
        return

    logger.info(f"cleanup_folders 计划删除 {len(folders_to_delete)} 个文件夹")
    for folder_path, folder_date in folders_to_delete:
        date_str = datetime.fromtimestamp(folder_date).strftime('%Y-%m-%d %H:%M:%S')
        logger.info(f"cleanup_folders - {folder_path} (日期: {date_str})")

    if dry_run:
        logger.info("cleanup_folders === 干运行模式，未执行实际删除操作 ===")
    else:
        logger.info("cleanup_folders 开始删除操作...")
        for folder_path, _ in folders_to_delete:
            try:
                shutil.rmtree(folder_path)
                logger.info(f"cleanup_folders 已删除: {folder_path}")
            except Exception as e:
                logger.error(f"cleanup_folders 删除失败 {folder_path}: {e}")

    logger.info(f"cleanup_folders 操作完成")


if __name__ == "__main__":
    cleanup_folders(
        "D:\\HWTreeATE\\log",
        keep_count=5,
        use_creation_time=True,
        dry_run=True
    )
