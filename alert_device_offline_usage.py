# -*- coding: utf-8 -*-
"""
<AUTHOR> Assistant
@Date   : 2025-01-28
@Desc   : alert_device_offline 方法使用示例
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fs_manager.FSManager import FSManager


def example_1_with_project_number():
    """示例1: 使用项目编号自动获取测试人员"""
    print("=" * 50)
    print("示例1: 使用项目编号自动获取测试人员")
    print("=" * 50)
    
    params = {
        "project_info": "RESSN10项目",
        "project_number": "RESSN10",  # 关键：提供项目编号
        "test_plan": "硬件功能测试",
        "tester": "自动获取",
        "machine_number": "HW-M001",
        "device_name": "主显示屏",
        "exception_desc": "设备连接超时，无法响应指令"
    }
    
    print("参数:")
    for key, value in params.items():
        print(f"  {key}: {value}")
    
    result = FSManager.alert_device_offline(params)
    print(f"\n发送结果: {'✅ 成功' if result else '❌ 失败'}")
    print("说明: 系统会自动调用 get_tester 接口获取项目的测试人员列表")


def example_2_fallback_to_default():
    """示例2: 获取测试人员失败时使用默认用户列表"""
    print("\n" + "=" * 50)
    print("示例2: 获取测试人员失败时使用默认用户列表")
    print("=" * 50)
    
    params = {
        "project_info": "未配置项目",
        "project_number": "NONEXISTENT_PROJECT",  # 不存在的项目
        "test_plan": "测试计划",
        "tester": "未知",
        "machine_number": "HW-M002",
        "device_name": "副显示屏",
        "exception_desc": "设备离线"
    }
    
    print("参数:")
    for key, value in params.items():
        print(f"  {key}: {value}")
    
    result = FSManager.alert_device_offline(params)
    print(f"\n发送结果: {'✅ 成功' if result else '❌ 失败'}")
    print("说明: 项目不存在时，系统会使用默认的管理员用户列表")


def example_3_with_additional_users():
    """示例3: 添加额外的通知用户"""
    print("\n" + "=" * 50)
    print("示例3: 添加额外的通知用户")
    print("=" * 50)
    
    params = {
        "project_info": "RESSN10项目",
        "project_number": "RESSN10",
        "test_plan": "关键功能测试",
        "tester": "项目测试人员",
        "machine_number": "HW-M003",
        "device_name": "触摸屏",
        "exception_desc": "触摸功能异常",
        "additional_users": [
            "ou_manager1",  # 项目经理
            "ou_manager2"   # 技术负责人
        ]
    }
    
    print("参数:")
    for key, value in params.items():
        print(f"  {key}: {value}")
    
    result = FSManager.alert_device_offline(params)
    print(f"\n发送结果: {'✅ 成功' if result else '❌ 失败'}")
    print("说明: 除了项目测试人员，还会通知额外指定的用户")


def example_4_without_project_number():
    """示例4: 不提供项目编号，使用默认用户列表"""
    print("\n" + "=" * 50)
    print("示例4: 不提供项目编号，使用默认用户列表")
    print("=" * 50)
    
    params = {
        "project_info": "通用测试",
        # 注意：没有提供 project_number
        "test_plan": "通用功能测试",
        "tester": "通用测试人员",
        "machine_number": "HW-M004",
        "device_name": "通用设备",
        "exception_desc": "设备状态异常"
    }
    
    print("参数:")
    for key, value in params.items():
        print(f"  {key}: {value}")
    
    result = FSManager.alert_device_offline(params)
    print(f"\n发送结果: {'✅ 成功' if result else '❌ 失败'}")
    print("说明: 没有项目编号时，直接使用默认的管理员用户列表")


def example_5_minimal_params():
    """示例5: 最少参数调用"""
    print("\n" + "=" * 50)
    print("示例5: 最少参数调用")
    print("=" * 50)
    
    params = {
        "project_number": "RESSN10",
        "device_name": "关键设备",
        "exception_desc": "设备故障"
    }
    
    print("参数:")
    for key, value in params.items():
        print(f"  {key}: {value}")
    
    result = FSManager.alert_device_offline(params)
    print(f"\n发送结果: {'✅ 成功' if result else '❌ 失败'}")
    print("说明: 只提供关键参数，其他参数会使用默认值")


def show_parameter_description():
    """显示参数说明"""
    print("\n" + "=" * 60)
    print("参数说明")
    print("=" * 60)
    
    params_desc = [
        ("project_info", "项目信息", "显示在通知中的项目描述"),
        ("project_number", "项目编号", "用于获取项目测试人员，如 'RESSN10'"),
        ("test_plan", "测试计划", "当前执行的测试计划名称"),
        ("tester", "测试人员", "当前测试人员（显示用）"),
        ("machine_number", "机台编号", "设备所在的机台编号"),
        ("device_name", "设备名称", "出现异常的设备名称"),
        ("exception_desc", "异常描述", "具体的异常描述信息"),
        ("additional_users", "额外用户", "额外需要通知的用户openId列表")
    ]
    
    print(f"{'参数名':<20} {'中文名':<12} {'说明'}")
    print("-" * 60)
    for param, chinese, desc in params_desc:
        print(f"{param:<20} {chinese:<12} {desc}")
    
    print("\n优化特性:")
    print("1. 🎯 自动获取项目测试人员：提供 project_number 时自动调用 get_tester 接口")
    print("2. 🛡️ 智能降级处理：获取失败时自动使用默认管理员列表")
    print("3. 📧 支持额外用户：可通过 additional_users 添加额外通知对象")
    print("4. 🔄 用户去重：自动去除重复的用户ID")
    print("5. 📝 详细日志：记录完整的执行过程和结果")
    print("6. ⚡ 异常处理：网络超时、连接失败等异常的优雅处理")


def main():
    """主函数"""
    print("alert_device_offline 方法使用示例")
    print("注意：以下示例会发送实际的飞书通知，请谨慎运行")
    
    choice = input("\n是否继续运行示例？(y/N): ").strip().lower()
    if choice != 'y':
        print("已取消运行")
        show_parameter_description()
        return
    
    try:
        # 设置固定的 Bearer Token（实际使用时应该通过登录获取）
        from fs_manager.FSManager import project_manager
        fixed_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUzNzc3OTQwLCJpYXQiOjE3NTM2OTE1NDAsImp0aSI6IjczMjA4Mzk4ZGE4MTQxNDc4MGU4NWYzY2YyMGVhYjIzIiwidXNlcl9pZCI6MTgxfQ.SHUv-BmzTOfSlX-GqFVckOscEjTZJIQpUv6HnZ1HwoI"
        original_token = project_manager.get_access_token()
        project_manager.set_access_token(fixed_token)
        
        # 运行示例
        example_1_with_project_number()
        
        input("\n按回车键继续下一个示例...")
        example_2_fallback_to_default()
        
        input("\n按回车键继续下一个示例...")
        example_3_with_additional_users()
        
        input("\n按回车键继续下一个示例...")
        example_4_without_project_number()
        
        input("\n按回车键继续下一个示例...")
        example_5_minimal_params()
        
        # 恢复原始 token
        project_manager.set_access_token(original_token)
        
    except Exception as e:
        print(f"运行异常: {str(e)}")
        import traceback
        traceback.print_exc()
    
    show_parameter_description()


if __name__ == '__main__':
    main()
