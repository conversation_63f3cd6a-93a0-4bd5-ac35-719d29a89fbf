import time
import pyqtgraph as pg
from PyQt5.QtGui import QColor


class CustomPlotWidget(pg.PlotWidget):
    """
    简化版自定义角度测量绘图组件
    兼容性更好，功能精简但实用
    """

    def __init__(self, parent=None):
        super(CustomPlotWidget, self).__init__(parent=parent)

        # 数据存储
        self.time_data = []
        self.angle_data = []  # 改为角度数据
        self.max_points = 1000  # 最大数据点数
        self.start_time = None  # 记录开始时间

        # 绘图对象
        self.curve = None

        # 初始化绘图区域
        self.init_plot()

    def init_plot(self):
        """初始化绘图区域设置"""
        # 设置背景颜色为白色
        # self.setBackground('black')
        self.setBackground(QColor(0, 0, 0))
        # 设置坐标轴标签
        self.setLabel('left', '角度 (°)')  # 改为角度
        self.setLabel('bottom', '时间 (s)')

        # 设置标题
        self.setTitle('角度实时监测')

        # 显示网格
        self.showGrid(x=True, y=True)

        # 启用自动范围调整
        self.enableAutoRange('xy', True)
        # # 只自动缩放X轴，不自动缩放Y轴
        # self.enableAutoRange('x', True)  # 只自动调整X轴
        # self.enableAutoRange('y', False)  # 禁止Y轴自适应

        # 固定Y轴范围为0~180度
        self.setYRange(0, 180)

    def add_data_point(self, time_value, angle_value):
        """
        添加单个数据点

        Args:
            time_value (float): 时间值
            angle_value (float): 角度值
        """
        self.time_data.append(time_value)
        self.angle_data.append(angle_value)

        # 限制数据点数量，防止内存溢出
        if len(self.time_data) > self.max_points:
            self.time_data = self.time_data[-self.max_points:]
            self.angle_data = self.angle_data[-self.max_points:]

        # 更新绘图
        self.update_plot()

    def set_data(self, time_data, angle_data):
        """
        设置完整的数据集

        Args:
            time_data (list): 时间数据列表
            angle_data (list): 角度数据列表
        """
        self.time_data = list(time_data)
        self.angle_data = list(angle_data)

        # 限制数据点数量
        if len(self.time_data) > self.max_points:
            self.time_data = self.time_data[-self.max_points:]
            self.angle_data = self.angle_data[-self.max_points:]

        self.update_plot()

    def update_plot(self):
        """更新绘图显示"""
        if len(self.time_data) == 0:
            return

        if self.curve is None:
            # 创建新的绘图曲线
            self.curve = self.plot(
                self.time_data,
                self.angle_data,
                pen=pg.mkPen('r', width=2),  # 蓝色线条
                symbol='o',  # 圆形标记
                symbolSize=3,  # 标记大小
                symbolBrush='r'  # 红色填充
            )
        else:
            # 更新现有曲线
            # print(self.time_data, self.angle_data)
            self.curve.setData(self.time_data[-self.max_points:], self.angle_data[-self.max_points:])

    def clear_data(self):
        """清空所有数据和绘图"""
        self.time_data.clear()
        self.angle_data.clear()
        self.clear()
        self.curve = None
        self.start_time = None

    def get_data(self):
        """
        获取当前数据

        Returns:
            tuple: (time_data, angle_data)
        """
        return self.time_data.copy(), self.angle_data.copy()

    def set_max_points(self, max_points):
        """
        设置最大数据点数

        Args:
            max_points (int): 最大数据点数
        """
        self.max_points = max_points

    def reset_start_time(self):
        """重置开始时间"""
        self.start_time = time.time()