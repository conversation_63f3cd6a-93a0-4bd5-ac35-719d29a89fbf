# CAN DBC 信号发送接收方法使用说明

## 概述

新增的 `execute_can_dbc` 方法支持基于DBC文件的CAN信号发送和接收，使用 cantools + python-can 库实现。

## 方法签名

```python
def execute_can_dbc(self, step):
```

## 参数说明

step 字典应包含以下参数：

### 必需参数
- `case_number`: 测试用例编号
- `command`: 命令名称
- `dbc_file`: DBC文件的完整路径
- `channel`: CAN通道号（整数）

### 发送消息参数
- `message_name`: 要发送的消息名称（在DBC文件中定义）
- `signals`: 信号字典，格式为 `{'Signal1': value1, 'Signal2': value2}`

### 接收验证参数（可选）
- `expect_message`: 期望接收的消息名称
- `expect_signals`: 期望的信号值字典，格式同 signals

## 使用示例

### 1. 仅发送消息

```python
step = {
    "case_number": "001",
    "command": "send_engine_data",
    "dbc_file": "path/to/engine.dbc",
    "message_name": "EngineData",
    "signals": {
        "EngineSpeed": 2000,
        "EngineTemp": 85.5,
        "ThrottlePos": 45.2
    },
    "channel": 1
}

step_manager.execute_can_dbc(step)
```

### 2. 发送消息并验证接收

```python
step = {
    "case_number": "002", 
    "command": "test_engine_response",
    "dbc_file": "path/to/engine.dbc",
    "message_name": "EngineCommand",
    "signals": {
        "CommandType": 1,
        "TargetSpeed": 1500
    },
    "expect_message": "EngineResponse",
    "expect_signals": {
        "ResponseCode": 0,
        "ActualSpeed": 1500
    },
    "channel": 1
}

step_manager.execute_can_dbc(step)
```

### 3. 仅接收验证消息

```python
step = {
    "case_number": "003",
    "command": "verify_status",
    "dbc_file": "path/to/vehicle.dbc", 
    "expect_message": "VehicleStatus",
    "expect_signals": {
        "SystemReady": 1,
        "ErrorCode": 0
    },
    "channel": 2
}

step_manager.execute_can_dbc(step)
```

## 支持的CAN设备

- 周立功 (zlg)
- PCAN
- CANoe
- TSMaster

## 注意事项

1. **DBC文件路径**: 确保DBC文件存在且路径正确
2. **消息名称**: 消息名称必须在DBC文件中定义
3. **信号名称**: 信号名称必须在对应消息中定义
4. **数据类型**: 信号值会根据DBC定义自动进行类型转换和编码
5. **接收验证**: 当前接收验证部分为框架代码，需要根据具体CAN设备实现

## 错误处理

方法会自动处理以下错误情况：
- CAN设备未连接
- DBC文件不存在或无法加载
- 消息名称不存在
- 信号编码失败
- 消息发送失败
- 接收验证失败

所有错误都会通过 signals_manager.step_execute_finish 信号发送结果。

## 依赖库

确保安装以下Python库：
```bash
pip install cantools python-can
```
