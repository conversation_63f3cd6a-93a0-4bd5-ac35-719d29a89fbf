# -*- coding: utf-8 -*-
"""
<AUTHOR> Assistant
@Date   : 2025-01-23
@Desc   : FSManager.get_tester 方法使用示例
"""

from fs_manager.FSManager import FSManager


def example_get_tester():
    """获取测试人员信息的示例"""
    
    print("=" * 60)
    print("FSManager.get_tester 方法使用示例")
    print("=" * 60)
    
    # 示例项目编号
    project_number = "RESSN10"
    
    print(f"正在获取项目 {project_number} 的测试人员信息...")
    print("-" * 40)
    
    try:
        # 调用 get_tester 方法
        status, data, err_msg = FSManager.get_tester(project_number)
        
        if status:
            print("✅ 获取测试人员信息成功！")
            print(f"返回数据: {data}")
            
            # 如果数据中包含测试人员列表
            if data and "testers" in data:
                testers = data["testers"]
                print(f"\n找到 {len(testers)} 个测试人员:")
                for i, tester in enumerate(testers, 1):
                    print(f"  {i}. 姓名: {tester.get('name', '未知')}")
                    print(f"     邮箱: {tester.get('email', '未知')}")
                    print(f"     ID: {tester.get('id', '未知')}")
                    print()
            else:
                print("数据格式可能不符合预期")
                
        else:
            print("❌ 获取测试人员信息失败！")
            print(f"错误信息: {err_msg}")
            
    except Exception as e:
        print(f"❌ 调用过程中发生异常: {str(e)}")
    
    print("=" * 60)


def example_error_handling():
    """错误处理示例"""
    
    print("=" * 60)
    print("错误处理示例")
    print("=" * 60)
    
    # 测试不同的错误情况
    test_cases = [
        ("", "空项目编号"),
        ("INVALID_PROJECT", "无效项目编号"),
        ("RESSN10", "正常项目编号")
    ]
    
    for project_number, description in test_cases:
        print(f"\n测试场景: {description}")
        print(f"项目编号: '{project_number}'")
        print("-" * 30)
        
        try:
            status, data, err_msg = FSManager.get_tester(project_number)
            
            if status:
                print("✅ 请求成功")
                if data:
                    print(f"数据类型: {type(data)}")
                    if isinstance(data, dict):
                        print(f"数据键: {list(data.keys())}")
            else:
                print("❌ 请求失败")
                print(f"错误信息: {err_msg}")
                
        except Exception as e:
            print(f"❌ 异常: {str(e)}")
    
    print("=" * 60)


def example_integration():
    """集成使用示例"""
    
    print("=" * 60)
    print("集成使用示例")
    print("=" * 60)
    
    def get_project_testers(project_number):
        """获取项目测试人员的封装函数"""
        status, data, err_msg = FSManager.get_tester(project_number)
        
        if not status:
            print(f"获取测试人员失败: {err_msg}")
            return []
        
        if not data or "testers" not in data:
            print("返回数据格式不正确")
            return []
        
        return data["testers"]
    
    def format_tester_info(tester):
        """格式化测试人员信息"""
        name = tester.get("name", "未知")
        email = tester.get("email", "未知")
        tester_id = tester.get("id", "未知")
        return f"{name} ({email}) [ID: {tester_id}]"
    
    # 使用示例
    project_number = "RESSN10"
    print(f"获取项目 {project_number} 的测试人员...")
    
    testers = get_project_testers(project_number)
    
    if testers:
        print(f"\n项目 {project_number} 的测试人员列表:")
        for i, tester in enumerate(testers, 1):
            formatted_info = format_tester_info(tester)
            print(f"  {i}. {formatted_info}")
    else:
        print("未找到测试人员或获取失败")
    
    print("=" * 60)


if __name__ == "__main__":
    print("FSManager.get_tester 方法使用示例")
    print("注意：由于网络环境限制，实际运行可能会失败")
    print("这些示例主要用于演示正确的调用方式")
    print()
    
    # 运行示例
    example_get_tester()
    print()
    example_error_handling()
    print()
    example_integration()
    
    print("\n使用说明:")
    print("1. 确保网络连接正常")
    print("2. 确保已正确配置 access_token")
    print("3. 确保 API 服务器可访问")
    print("4. 根据实际 API 返回格式调整数据处理逻辑")
