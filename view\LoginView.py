import os.path
import threading

from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPixmap
from PyQt5.QtWidgets import QWidget, QLineEdit

from common.LogUtils import logger
from common.view.MessageDialog import MessageDialog
from res import global_label_stylesheet
from utils.SignalsManager import signals_manager
from fs_manager.FSManager import fs_manager
from ui.UiLogin import Ui_FormLogin
from utils.ProjectManager import project_manager
from view.HomePage import MainWindow


class LoginView(QWidget, Ui_FormLogin):

    def __init__(self):
        super(LoginView, self).__init__()
        self.setupUi(self)
        self.lineEditUser.setFocus()
        self.pushButtonLogin.clicked.connect(self.login)
        self.logo_label.setFixedSize(180, 50)
        self.logo_label.setScaledContents(True)
        self.logo_label.setPixmap(QPixmap(os.path.join(os.getcwd(), "images", "icon_logo.png")))
        self.main_window = MainWindow()
        self.is_active_close = True
        self.setWindowTitle("登录")
        self.setWindowFlags(Qt.FramelessWindowHint)
        # 设置Tab键顺序
        self.lineEditUser.setTabOrder(self.lineEditUser, self.lineEditPassWord)
        self.lineEditUser.setStyleSheet("color:#D0D0D0; height:60; font-size:11pt; border:none")
        self.lineEditPassWord.setTabOrder(self.lineEditPassWord, self.pushButtonLogin)
        self.lineEditPassWord.setStyleSheet("color:#D0D0D0; height:60; font-size:11pt; border:none")
        self.label.setStyleSheet("margin-top:20; color:#D0D0D0")

    def show_pwd(self):
        logger.info(f"show_pwd mode={self.lineEditPassWord.echoMode()}")
        if self.lineEditPassWord.echoMode() == QLineEdit.EchoMode.Password:
            self.lineEditPassWord.setEchoMode(QLineEdit.EchoMode.Normal)
        else:
            self.lineEditPassWord.setEchoMode(QLineEdit.EchoMode.Password)

    def login(self):
        user = self.lineEditUser.text()
        pwd = self.lineEditPassWord.text()
        if not user or not pwd:
            MessageDialog.show_auto_close_message("提示", "请输入用户名和密码", 3000)
            return
        if user == "admin" and pwd == "123456":
            self.is_active_close = False
            project_manager.set_test_user(user)
            self.open_main_window()
            return
        if "@hwtc.com.cn" not in user and "@hiway.com" not in user:
            user = user + "@hwtc.com.cn"

        status, response = fs_manager.login_with_pwd(user, pwd)
        logger.info(f"login status={status}, response={response}")
        if status and response.get("err_code", -1) == 0:
            project_manager.set_access_token(response["data"]["access_token"])
            project_manager.set_test_user(response["data"]["user_info"]["username"])
            project_manager.set_test_email(response["data"]["user_info"]["email"])
            self.is_active_close = False
            threading.Timer(interval=12 * 60 * 60, function=fs_manager.auto_login, args=(user, pwd)).start()
            # 登录成功后打开主窗口
            self.open_main_window()
            # 拉取测试计划
            signals_manager.pull_plan.emit()
        else:
            err_code = response.get("err_code", 404)
            msg = response.get("msg", "网络故障中，休息一会吧！")
            MessageDialog.show_auto_close_message("提示", f"错误码：{err_code}\n\n错误：{msg}", 3000)

    def keyPressEvent(self, event):
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            self.login()
        super().keyPressEvent(event)

    def open_main_window(self):
        self.main_window.showMaximized()
        self.main_window.show_system_config()
        threading.Thread(target=fs_manager.proxy_get_projects).start()
        self.close()

    def closeEvent(self, a0) -> None:
        if self.is_active_close:
            self.main_window.closeEvent(a0)


if __name__ == '__main__':
    from PyQt5.QtWidgets import QApplication
    import sys

    app = QApplication(sys.argv)
    login = LoginView()
    login.show()
    sys.exit(app.exec_())
