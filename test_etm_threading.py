#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 ETM MU3 客户端的多线程并发问题
"""

import threading
import time
import logging
from power.tools.etm_mu3_client import client

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(threadName)s - %(levelname)s - %(message)s'
)

def test_concurrent_access():
    """测试并发访问"""
    
    def worker(worker_id, iterations=10):
        """工作线程函数"""
        for i in range(iterations):
            try:
                # 模拟读取操作
                result = client.read_hr_one(0x6d)  # 读取电压
                print(f"Worker {worker_id}, Iteration {i}: {result}")
                time.sleep(0.1)  # 短暂延时
            except Exception as e:
                print(f"Worker {worker_id}, Iteration {i} Error: {e}")
    
    # 首先连接设备
    print("正在连接设备...")
    success = client.open("ETM_MU3_TEST", "COM3", baudrate=9600, slave_id=1, timeout=5.0)
    if not success:
        print("设备连接失败!")
        return
    
    print("设备连接成功，开始并发测试...")
    
    # 创建多个线程进行并发测试
    threads = []
    for i in range(3):  # 创建3个工作线程
        thread = threading.Thread(target=worker, args=(i, 5))
        threads.append(thread)
    
    # 启动所有线程
    start_time = time.time()
    for thread in threads:
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    end_time = time.time()
    print(f"并发测试完成，耗时: {end_time - start_time:.2f}秒")
    
    # 关闭连接
    client.close()
    print("设备连接已关闭")

if __name__ == "__main__":
    test_concurrent_access()
