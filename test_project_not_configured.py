# -*- coding: utf-8 -*-
"""
<AUTHOR> Assistant
@Date   : 2025-01-28
@Desc   : 测试项目相关人未配置的情况
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fs_manager.FSManager import FSManager, project_manager


def test_project_not_configured():
    """测试项目相关人未配置的情况"""
    print("=" * 60)
    print("测试项目相关人未配置的情况")
    print("=" * 60)
    
    # 设置固定的 Bearer Token
    fixed_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUzNzc3OTQwLCJpYXQiOjE3NTM2OTE1NDAsImp0aSI6IjczMjA4Mzk4ZGE4MTQxNDc4MGU4NWYzY2YyMGVhYjIzIiwidXNlcl9pZCI6MTgxfQ.SHUv-BmzTOfSlX-GqFVckOscEjTZJIQpUv6HnZ1HwoI"
    
    # 保存原始 token
    original_token = project_manager.get_access_token()
    
    try:
        # 设置固定 token
        project_manager.set_access_token(fixed_token)
        
        # 测试用例
        test_cases = [
            ("RESSN10", "正常项目 - 应该成功"),
            ("NONEXISTENT", "不存在的项目 - 可能返回项目相关人未配置"),
            ("TEST123", "测试项目 - 可能返回项目相关人未配置"),
            ("", "空项目编号 - 应该被参数验证拦截"),
            ("   ", "空格项目编号 - 应该被参数验证拦截"),
        ]
        
        for project_number, description in test_cases:
            print(f"\n测试用例: {description}")
            print(f"项目编号: '{project_number}'")
            print("-" * 40)
            
            try:
                status, data, err_msg = FSManager.get_tester(project_number)
                
                print(f"请求状态: {'✅ 成功' if status else '❌ 失败'}")
                print(f"错误信息: {err_msg}")
                
                if status and data:
                    if isinstance(data, list):
                        print(f"测试人员数量: {len(data)}")
                        for i, tester in enumerate(data[:2], 1):  # 只显示前2个
                            print(f"  {i}. {tester.get('name', 'N/A')} ({tester.get('email', 'N/A')})")
                        if len(data) > 2:
                            print(f"  ... 还有 {len(data) - 2} 个测试人员")
                    else:
                        print(f"返回数据类型: {type(data)}")
                else:
                    print("无返回数据")
                    
            except Exception as e:
                print(f"异常: {str(e)}")
                import traceback
                traceback.print_exc()
        
        print("\n" + "=" * 60)
        print("测试完成")
        
        # 演示错误处理逻辑
        print("\n错误处理逻辑演示:")
        print("1. err_code=0: 成功获取测试人员信息")
        print("2. err_code=1 且 msg='项目相关人未配置': 显示友好的错误提示")
        print("3. 其他 err_code: 显示服务器错误信息")
        print("4. HTTP 4xx/5xx: 显示对应的错误类型")
        print("5. 网络异常: 显示网络相关错误")
        
    finally:
        # 恢复原始 token
        project_manager.set_access_token(original_token)
        print(f"\n已恢复原始 token")


if __name__ == '__main__':
    test_project_not_configured()
