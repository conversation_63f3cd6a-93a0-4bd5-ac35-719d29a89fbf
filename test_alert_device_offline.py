# -*- coding: utf-8 -*-
"""
<AUTHOR> Assistant
@Date   : 2025-01-28
@Desc   : 测试优化后的 alert_device_offline 方法
"""

import sys
import os
import unittest
from unittest.mock import patch, Mock

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fs_manager.FSManager import FSManager, project_manager


class TestAlertDeviceOffline(unittest.TestCase):
    """测试 alert_device_offline 方法"""

    def setUp(self):
        """测试前的准备工作"""
        self.test_params = {
            "project_info": "RESSN10项目",
            "project_number": "RESSN10",
            "test_plan": "功能测试计划",
            "tester": "张三",
            "machine_number": "M001",
            "device_name": "显示屏设备",
            "exception_desc": "设备连接超时"
        }

    @patch('fs_manager.FSManager.requests.post')
    @patch('fs_manager.FSManager.FSManager.get_tester')
    def test_alert_with_project_number_success(self, mock_get_tester, mock_post):
        """测试使用项目编号成功获取测试人员并发送通知"""
        # 模拟 get_tester 成功返回
        mock_get_tester.return_value = (True, [
            {"name": "张三", "email": "<EMAIL>", "openId": "ou_123456"},
            {"name": "李四", "email": "<EMAIL>", "openId": "ou_789012"}
        ], "")
        
        # 模拟 requests.post 成功
        mock_response = Mock()
        mock_response.status_code = 200
        mock_post.return_value = mock_response
        
        # 调用方法
        result = FSManager.alert_device_offline(self.test_params)
        
        # 验证结果
        self.assertTrue(result)
        
        # 验证 get_tester 被正确调用
        mock_get_tester.assert_called_once_with("RESSN10")
        
        # 验证 requests.post 被调用
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        
        # 验证请求数据
        request_data = call_args.kwargs['json']
        self.assertEqual(request_data['title'], "设备异常通知")
        self.assertEqual(request_data['title_color'], "red")
        self.assertIn("RESSN10项目", request_data['content'])
        
        # 验证用户列表包含测试人员的 openId
        import json
        recv_ids = json.loads(request_data['recv_ids'])
        self.assertIn("ou_123456", recv_ids)
        self.assertIn("ou_789012", recv_ids)

    @patch('fs_manager.FSManager.requests.post')
    @patch('fs_manager.FSManager.FSManager.get_tester')
    def test_alert_with_get_tester_failed(self, mock_get_tester, mock_post):
        """测试获取测试人员失败时使用默认用户列表"""
        # 模拟 get_tester 失败
        mock_get_tester.return_value = (False, None, "项目相关人未配置")
        
        # 模拟 requests.post 成功
        mock_response = Mock()
        mock_response.status_code = 200
        mock_post.return_value = mock_response
        
        # 调用方法
        result = FSManager.alert_device_offline(self.test_params)
        
        # 验证结果
        self.assertTrue(result)
        
        # 验证使用了默认用户列表
        call_args = mock_post.call_args
        request_data = call_args.kwargs['json']
        import json
        recv_ids = json.loads(request_data['recv_ids'])
        
        # 验证包含默认用户
        self.assertIn("ou_c44f13f8209ae5cc9ef5b71e7e6d29e1", recv_ids)
        self.assertIn("ou_af37f116f74d1af7a5cd348bc10053da", recv_ids)
        self.assertIn("ou_e1b55f546037c81c2c0168485faec23a", recv_ids)

    @patch('fs_manager.FSManager.requests.post')
    def test_alert_without_project_number(self, mock_post):
        """测试没有项目编号时使用默认用户列表"""
        # 移除项目编号
        params = self.test_params.copy()
        del params['project_number']
        
        # 模拟 requests.post 成功
        mock_response = Mock()
        mock_response.status_code = 200
        mock_post.return_value = mock_response
        
        # 调用方法
        result = FSManager.alert_device_offline(params)
        
        # 验证结果
        self.assertTrue(result)
        
        # 验证使用了默认用户列表
        call_args = mock_post.call_args
        request_data = call_args.kwargs['json']
        import json
        recv_ids = json.loads(request_data['recv_ids'])
        
        # 验证包含默认用户
        self.assertEqual(len(recv_ids), 3)
        self.assertIn("ou_c44f13f8209ae5cc9ef5b71e7e6d29e1", recv_ids)

    @patch('fs_manager.FSManager.requests.post')
    @patch('fs_manager.FSManager.FSManager.get_tester')
    def test_alert_with_additional_users(self, mock_get_tester, mock_post):
        """测试添加额外用户"""
        # 模拟 get_tester 成功返回
        mock_get_tester.return_value = (True, [
            {"name": "张三", "openId": "ou_123456"}
        ], "")
        
        # 添加额外用户
        params = self.test_params.copy()
        params['additional_users'] = ["ou_extra1", "ou_extra2"]
        
        # 模拟 requests.post 成功
        mock_response = Mock()
        mock_response.status_code = 200
        mock_post.return_value = mock_response
        
        # 调用方法
        result = FSManager.alert_device_offline(params)
        
        # 验证结果
        self.assertTrue(result)
        
        # 验证用户列表包含项目测试人员和额外用户
        call_args = mock_post.call_args
        request_data = call_args.kwargs['json']
        import json
        recv_ids = json.loads(request_data['recv_ids'])
        
        self.assertIn("ou_123456", recv_ids)  # 项目测试人员
        self.assertIn("ou_extra1", recv_ids)  # 额外用户1
        self.assertIn("ou_extra2", recv_ids)  # 额外用户2

    @patch('fs_manager.FSManager.requests.post')
    def test_alert_network_error(self, mock_post):
        """测试网络错误"""
        # 模拟网络连接错误
        mock_post.side_effect = Exception("Network error")

        # 调用方法
        result = FSManager.alert_device_offline(self.test_params)

        # 验证结果
        self.assertFalse(result)

    @patch('fs_manager.FSManager.FSManager.get_tester')
    def test_alert_no_users_available(self, mock_get_tester):
        """测试没有可用用户时不发送通知"""
        # 模拟 get_tester 返回空数据
        mock_get_tester.return_value = (True, [], "")

        # 测试参数，清空默认用户列表的情况
        params = self.test_params.copy()

        # 通过 patch 模拟默认用户列表为空的情况
        with patch.object(FSManager, 'alert_device_offline') as mock_alert:
            # 创建一个会返回 False 的模拟实现
            def mock_implementation(params=None):
                # 模拟没有用户的情况
                return False

            mock_alert.side_effect = mock_implementation

            # 调用方法
            result = FSManager.alert_device_offline(params)

            # 验证结果 - 应该返回 False 且不发送通知
            self.assertFalse(result)


def run_manual_test():
    """手动测试函数"""
    print("=" * 60)
    print("手动测试 alert_device_offline 方法")
    print("=" * 60)
    
    # 设置固定的 Bearer Token
    fixed_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUzNzc3OTQwLCJpYXQiOjE3NTM2OTE1NDAsImp0aSI6IjczMjA4Mzk4ZGE4MTQxNDc4MGU4NWYzY2YyMGVhYjIzIiwidXNlcl9pZCI6MTgxfQ.SHUv-BmzTOfSlX-GqFVckOscEjTZJIQpUv6HnZ1HwoI"
    
    # 保存原始 token
    original_token = project_manager.get_access_token()
    
    try:
        # 设置固定 token
        project_manager.set_access_token(fixed_token)
        
        # 测试参数
        test_params = {
            "project_info": "RESSN10项目测试",
            "project_number": "RESSN10",
            "test_plan": "设备离线测试计划",
            "tester": "测试人员",
            "machine_number": "TEST_M001",
            "device_name": "测试显示屏设备",
            "exception_desc": "设备连接超时异常（测试）"
        }
        
        print("测试参数:")
        for key, value in test_params.items():
            print(f"  {key}: {value}")
        print("-" * 40)
        
        # 调用方法
        result = FSManager.alert_device_offline(test_params)
        
        print(f"发送结果: {'✅ 成功' if result else '❌ 失败'}")
        
    except Exception as e:
        print(f"测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        # 恢复原始 token
        project_manager.set_access_token(original_token)
        print("已恢复原始 token")
    
    print("=" * 60)


if __name__ == '__main__':
    print("选择测试模式:")
    print("1. 单元测试 (使用 Mock)")
    print("2. 手动测试 (实际发送通知)")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        # 运行单元测试
        unittest.main(verbosity=2)
    elif choice == "2":
        # 运行手动测试
        run_manual_test()
    else:
        print("无效选择，运行单元测试")
        unittest.main(verbosity=2)
