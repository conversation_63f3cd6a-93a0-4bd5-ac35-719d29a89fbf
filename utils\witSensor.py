#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
串口传感器数据采集解析工具
支持实时获取串口数据并解析传感器协议
"""
import traceback

import serial
import serial.tools.list_ports
import threading
import time
import queue
from typing import List, Dict, Optional
import sys
from datetime import datetime

from PyQt5.QtCore import QObject, pyqtSignal

from common.LogUtils import logger
from utils.Influxdb import influx_client

parsed_arg = "Y"
class SensorDataParser:
    """传感器数据协议解析器"""

    # 数据类型映射
    TYPE_MAP = {
        0x50: "时间",
        0x51: "加速度",
        0x52: "角速度",
        0x53: "角度",
        0x54: "磁场",
        0x55: "端口状态",
        0x56: "气压高度",
        0x57: "经纬度",
        0x58: "地速",
        0x59: "四元数",
        0x5A: "GPS定位精度",
        0x5F: "读取"
    }

    def __init__(self):
        self.packet_size = 11
        self.header = 0x55

    def bytes_to_signed_short(self, low_byte: int, high_byte: int) -> int:
        """将低字节和高字节组合成有符号的short类型数据"""
        if high_byte > 127:
            high_signed = high_byte - 256
        else:
            high_signed = high_byte

        result = (high_signed << 8) | low_byte

        if result > 32767:
            result -= 65536
        elif result < -32768:
            result += 65536

        return result

    def calculate_checksum(self, packet: List[int]) -> int:
        """计算数据包校验和"""
        checksum = sum(packet[:10])
        return checksum & 0xFF

    def parse_single_packet(self, packet: List[int]) -> Optional[Dict]:
        """解析单个数据包"""
        if len(packet) != self.packet_size or packet[0] != self.header:
            return None

        header = packet[0]
        data_type = packet[1]

        data1 = self.bytes_to_signed_short(packet[2], packet[3])
        data2 = self.bytes_to_signed_short(packet[4], packet[5])
        data3 = self.bytes_to_signed_short(packet[6], packet[7])
        data4 = self.bytes_to_signed_short(packet[8], packet[9])

        received_checksum = packet[10]
        calculated_checksum = self.calculate_checksum(packet)

        return {
            'timestamp': datetime.now().strftime('%H:%M:%S.%f')[:-3],
            'header': f'0x{header:02X}',
            'type': f'0x{data_type:02X}',
            'type_name': self.TYPE_MAP.get(data_type, "未知"),
            'data1': data1,
            'data2': data2,
            'data3': data3,
            'data4': data4,
            'received_checksum': f'0x{received_checksum:02X}',
            'calculated_checksum': f'0x{calculated_checksum:02X}',
            'checksum_valid': received_checksum == calculated_checksum,
            'raw_bytes': [f'{b:02X}' for b in packet]
        }

    # def format_sensor_data(self, parsed_packet: Dict) -> str:
    #     """格式化传感器数据显示"""
    #     type_name = parsed_packet['type_name']
    #     data1, data2, data3, data4 = parsed_packet['data1'], parsed_packet['data2'], parsed_packet['data3'], \
    #     parsed_packet['data4']
    #
    #     if type_name == "加速度":
    #         return f"X: {data1 / 32768.0 * 16:.3f}g, Y: {data2 / 32768.0 * 16:.3f}g, Z: {data3 / 32768.0 * 16:.3f}g, T: {data4 / 100.0:.1f}°C"
    #     elif type_name == "角速度":
    #         return f"X: {data1 / 32768.0 * 2000:.1f}°/s, Y: {data2 / 32768.0 * 2000:.1f}°/s, Z: {data3 / 32768.0 * 2000:.1f}°/s, T: {data4 / 100.0:.1f}°C"
    #     elif type_name == "角度":
    #         return f"X: {data1 / 32768.0 * 180:.2f}°, Y: {data2 / 32768.0 * 180:.2f}°, Z: {data3 / 32768.0 * 180:.2f}°, T: {data4 / 100.0:.1f}°C"
    #     elif type_name == "磁场":
    #         return f"X: {data1}μT, Y: {data2}μT, Z: {data3}μT, T: {data4 / 100.0:.1f}°C"
    #     elif type_name == "气压高度":
    #         return f"气压: {data1}Pa, 海拔: {data2 / 100.0:.1f}m, 温度: {data3 / 100.0:.1f}°C, 高度: {data4 / 100.0:.1f}m"
    #     elif type_name == "经纬度":
    #         return f"纬度: {(data1 << 16 | data2) / 10000000.0:.7f}°, 经度: {(data3 << 16 | data4) / 10000000.0:.7f}°"
    #     elif type_name == "地速":
    #         return f"东向: {data1 / 100.0:.2f}m/s, 北向: {data2 / 100.0:.2f}m/s, 天向: {data3 / 100.0:.2f}m/s, 地速: {data4 / 100.0:.2f}m/s"
    #     elif type_name == "四元数":
    #         return f"q0: {data1 / 32768.0:.4f}, q1: {data2 / 32768.0:.4f}, q2: {data3 / 32768.0:.4f}, q3: {data4 / 32768.0:.4f}"
    #     else:
    #         return f"DATA1: {data1}, DATA2: {data2}, DATA3: {data3}, DATA4: {data4}"

    def format_sensor_data(self, parsed_packet: Dict) -> Dict[str, float]:
        """格式化传感器数据显示，返回字典格式，值为数值"""
        type_name = parsed_packet['type_name']
        data1, data2, data3, data4 = parsed_packet['data1'], parsed_packet['data2'], parsed_packet['data3'], \
            parsed_packet['data4']

        if type_name == "加速度":
            return {
                "X": data1 / 32768.0 * 16,
                "Y": data2 / 32768.0 * 16,
                "Z": data3 / 32768.0 * 16,
                "T": data4 / 100.0
            }
        elif type_name == "角速度":
            return {
                "X": data1 / 32768.0 * 2000,
                "Y": data2 / 32768.0 * 2000,
                "Z": data3 / 32768.0 * 2000,
                "T": data4 / 100.0
            }
        elif type_name == "角度":
            return {
                "X": data1 / 32768.0 * 180,
                "Y": data2 / 32768.0 * 180,
                "Z": data3 / 32768.0 * 180,
                "T": data4 / 100.0
            }
        elif type_name == "磁场":
            return {
                "X": float(data1),
                "Y": float(data2),
                "Z": float(data3),
                "T": data4 / 100.0
            }
        elif type_name == "气压高度":
            return {
                "气压": float(data1),
                "海拔": data2 / 100.0,
                "温度": data3 / 100.0,
                "高度": data4 / 100.0
            }
        elif type_name == "经纬度":
            return {
                "纬度": (data1 << 16 | data2) / 10000000.0,
                "经度": (data3 << 16 | data4) / 10000000.0
            }
        elif type_name == "地速":
            return {
                "东向": data1 / 100.0,
                "北向": data2 / 100.0,
                "天向": data3 / 100.0,
                "地速": data4 / 100.0
            }
        elif type_name == "四元数":
            return {
                "q0": data1 / 32768.0,
                "q1": data2 / 32768.0,
                "q2": data3 / 32768.0,
                "q3": data4 / 32768.0
            }
        else:
            return {
                "DATA1": float(data1),
                "DATA2": float(data2),
                "DATA3": float(data3),
                "DATA4": float(data4)
            }


class WitSerialSensorTool(QObject):
    sensor_data_changed = pyqtSignal(dict)
    """串口传感器数据采集工具"""

    def __init__(self):
        # super().__init__()
        super(WitSerialSensorTool, self).__init__()
        self.parser = SensorDataParser()
        self.serial_port = None
        self.running = False
        self.data_queue = queue.Queue()
        self.parsed_data_queue = queue.Queue()
        self.read_thread = None
        self.process_thread = None
        self.buffer = bytearray()
        self.is_connected = False
        self.current_sensor_data = {}
        # 统计信息
        self.total_packets = 0
        self.valid_packets = 0
        self.invalid_packets = 0
        self.start_time = None


    def connect_serial(self, port: str, baudrate: int = 921600, timeout: float = 1.0) -> bool:
        """连接串口"""
        try:
            self.serial_port = serial.Serial(
                port=port,
                baudrate=baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=timeout,
                xonxoff=False,
                rtscts=False,
                dsrdtr=False
            )

            if self.serial_port.is_open:
                print(f"成功连接串口: {port}, 波特率: {baudrate}")
                return True
            else:
                print(f"无法打开串口: {port}")
                return False

        except serial.SerialException as e:
            print(f"串口连接错误: {e}")
            return False
        except Exception as e:
            print(f"未知错误: {e}")
            return False

    def disconnect_serial(self,close=False):
        """断开串口连接"""
        self.running = False

        # if self.read_thread and self.read_thread.is_alive():
        #     self.read_thread.join(timeout=2)
        #
        # if self.process_thread and self.process_thread.is_alive():
        #     self.process_thread.join(timeout=2)


        return True

    def read_serial_data(self):
        """串口数据读取线程"""
        while self.running and self.serial_port and self.serial_port.is_open:
            try:
                if self.serial_port.in_waiting > 0:
                    data = self.serial_port.read(self.serial_port.in_waiting)
                    if data:
                        self.data_queue.put(data)
                    # time.sleep(0.1)
                else:
                    time.sleep(0.01)  # 1ms延时，避免CPU占用过高
            except serial.SerialException as e:
                print(f"串口读取错误: {e}")
                break
            except Exception as e:
                print(f"数据读取异常: {e}")
                break

    def process_serial_data(self):
        """数据处理线程"""
        while self.running:
            try:
                # 从队列获取数据，超时1秒
                # print("qsize",self.data_queue.qsize())
                if self.data_queue.empty():
                    time.sleep(.01)
                    continue
                # print("process_serial_data",self.data_queue.qsize())
                data = self.data_queue.get(timeout=1)
                self.buffer.extend(data)
                self.parse_buffer()
            except queue.Empty:
                time.sleep(.001)
                continue
            except Exception:
                print(traceback.format_exc())


    def parse_buffer(self):
        """解析缓冲区数据"""
        while len(self.buffer) >= self.parser.packet_size:
            # 寻找协议头
            header_index = -1
            for i in range(len(self.buffer)):
                if self.buffer[i] == self.parser.header:
                    header_index = i
                    break

            if header_index == -1:
                # 没有找到协议头，清空缓冲区
                self.buffer.clear()
                break

            # 移除协议头之前的无效数据
            if header_index > 0:
                self.buffer = self.buffer[header_index:]

            # 检查是否有完整的数据包
            if len(self.buffer) < self.parser.packet_size:
                break

            # 提取一个完整的数据包
            packet_bytes = list(self.buffer[:self.parser.packet_size])
            self.buffer = self.buffer[self.parser.packet_size:]

            # 解析数据包
            parsed_packet = self.parser.parse_single_packet(packet_bytes)
            self.total_packets += 1

            if parsed_packet:
                self.valid_packets += 1
                self.display_packet(parsed_packet)
            else:
                self.invalid_packets += 1

    def display_packet(self, packet: Dict,filter_type_name:str ="角度" ):
        """显示解析后的数据包"""
        status = 0 if packet['checksum_valid'] else 1 # 0 无错误 1 有错误
        formatted_data = self.parser.format_sensor_data(packet)
        # formatted_data.update({"status":status})
        item= {}
        item["type_name"] = packet['type_name']
        item["value"] = formatted_data
        item["status"] = status
        # print(item)
        if item["type_name"] ==filter_type_name:
            self.current_sensor_data = item
            # if filter_type_name == "角度":
            #     value = formatted_data["X"]
                # if self.total_packets//3==0:
                # self.upload_record_data(value)

        # print(f"[{packet['timestamp']}] {packet['type_name']:>6} | {formatted_data} ")
        # self.parsed_data_queue.put(item)
        # print(packet['type_name'],formatted_data)
        # 如果校验和错误，显示详细信息
        # if not packet['checksum_valid']:
        #     print(f"    校验和错误: 接收={packet['received_checksum']}, 计算={packet['calculated_checksum']}")
        #     print(f"    原始数据: {' '.join(packet['raw_bytes'])}")

    def upload_record_data(self,):
        try:
            from case.StepManager import step_manager
            from utils.ProjectManager import project_manager
            step = step_manager.get_current_step()
            case_number = step.get("case_number", "")
            case_name = step.get("case_name", "")
            project_number = project_manager.get_test_plan_project_number()
            project_name = project_manager.get_test_plan_project_name()
            test_plan_name = project_manager.get_test_plan_name()
            test_plan_id = project_manager.get_test_plan_id()
            machine_number = project_manager.get_machine_number()
        except Exception:
            logger.warning(f"upload_record_data error:{traceback.format_exc()}")
            return

        try:
            if self.current_sensor_data["type_name"] == "角度":
                value = self.current_sensor_data['value']['X']
                value = round(value, 2)
                influx_client.write_data_multi(
                    table=str(project_number),
                    tags={
                        "project_name": project_name,
                        "test_plan_name": test_plan_name,
                        "test_plan_id": test_plan_id,
                        "machine_number": machine_number,
                        "case_number": case_number,
                        "case_name": case_name,
                        # "channel": f"chl{i + 1}"
                    },
                    fields={"work_angle": float(value)}
                )
                # logger.info(f"upload_record_data toInfluxDB:  {value}")
            if self.running and self.serial_port and self.serial_port.is_open:
                threading.Timer(0.1, self.upload_record_data).start()
        except Exception:
            logger.warning(f"update_work_current2influxdb error: {traceback.format_exc()}")  # 记录错误日志，以便调试和排除问题

    def open(self, port: str, baudrate: int = 921600):
        """开始监控串口数据"""
        if not self.connect_serial(port, baudrate):
            self.is_connected = False
            return False
        self.is_connected = True
        return True
    def close(self):
        self.disconnect_serial()
        if self.serial_port and self.serial_port.is_open:
            self.serial_port.close()
            print("串口已关闭")
            return True

    def stop_monitoring(self):
        self.disconnect_serial()

    def start_monitoring(self):
        """开始监控串口数据"""
        # if not self.connect_serial(port, baudrate):
        #     return False
        #
        self.running = True
        # self.start_time = time.time()

        # 启动数据读取线程
        self.read_thread = threading.Thread(target=self.read_serial_data, name ="read_serial_data")
        self.read_thread.start()

        # 启动数据处理线程
        self.process_thread = threading.Thread(target=self.process_serial_data, name ="process_serial_data")
        self.process_thread.start()
        # try:
        #     while self.running:
        #         time.sleep(1)
        # except KeyboardInterrupt:
        #     print(f"\n\n停止监控...")
        #     self.disconnect_serial()
        #     self.show_statistics()

        return True

wit_serial_sensor = WitSerialSensorTool()
def main():
    """主函数"""
    tool = WitSerialSensorTool()
    try:
        # 获取用户输入
        port = "COM23"
        baudrate = 921600
        tool.open(port, baudrate)
        # 开始监控
        tool.start_monitoring()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行错误: {e}")
    # finally:
    #     tool.disconnect_serial()


if __name__ == "__main__":
    # value = 8.01 - abs(8.01 - 7.91) * 1.382
    # print(value)
    main()