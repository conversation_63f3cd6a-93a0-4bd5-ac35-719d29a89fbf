# -*- coding: utf-8 -*-
"""
user:Created by jid on 2021/3/25 17:50
email:<EMAIL>
description:
"""
import ctypes as ct
import logging
import os
import sys


# logging.basicConfig(level=logging.DEBUG)
# logger = logging.getLogger('WhiteBalanceServer')


#os.environ['path'] = r"C:\work\HWTreeATE\photics\dll" + os.pathsep + os.environ['PATH']

class SysHXCTAManager:

    def __init__(self):
        super(SysHXCTAManager, self).__init__()
        self.SysHXCTA = None

        self.x = 0.305
        self.y = 0.320
        self.x_dict = {}
        self.y_dict = {}
        self.y_dw_limit = 0.3
        self.y_up_limit = 1.0
        self.y_dw_limit_dict = {}
        self.y_up_limit_dict = {}
        self.mcu_type = None

    def load_dll(self, mcu_type="HX82115A"):
        """加载DLL文件"""
        dll_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), 'dll'))
        self.mcu_type = mcu_type
        dll_path = os.path.join(dll_dir, f'{mcu_type}.dll')

        if mcu_type == "HX83192C":
            # SetDllDirectoryW 解决依赖链问题（Python 3.6兼容）
            try:
                kernel32 = ct.WinDLL('kernel32', use_last_error=True)
                kernel32.SetDllDirectoryW.argtypes = [ct.c_wchar_p]
                kernel32.SetDllDirectoryW.restype = ct.c_bool
                if not kernel32.SetDllDirectoryW(dll_dir):
                    error = ct.WinError(ct.get_last_error())
                    logging.error(f"设置 DLL 搜索路径失败 (路径: '{dll_dir}'). 错误: {error}")
                    raise error
                self.SysHXCTA = ct.WinDLL(dll_path)
            except:
                self.SysHXCTA = ct.WinDLL(dll_path)
        else:
            # 其他DLL使用winmode方案
            try:
                self.SysHXCTA = ct.WinDLL(dll_path, winmode=0x00000800)
            except Exception:
                self.SysHXCTA = ct.WinDLL(dll_path)

        logging.info('Successfully loaded DLL for %s' % mcu_type)

    def update_color_coordinate(self, device_no: str, wb_params: list):
        logging.info('update_color_coordinate device_no=%s, wb_params=%s' % (device_no, wb_params))
        if len(wb_params) == 2:
            self.x_dict.update({device_no: wb_params[0]})
            self.y_dict.update({device_no: wb_params[1]})
        elif len(wb_params) == 4:
            self.x_dict.update({device_no: wb_params[0]})
            self.y_dict.update({device_no: wb_params[1]})
            self.y_dw_limit_dict.update({device_no: wb_params[2]})
            self.y_up_limit_dict.update({device_no: wb_params[3]})

    def get_version_support_ic(self):
        version_p = ct.c_char_p(''.encode())
        version_state = self.SysHXCTA.DLL_Version(version_p)
        logging.info("get_version_support_ic version_state=%s", version_state)
        logging.info("get_version_support_ic DLL_Version=%s", version_p.value.decode())

        ic_list_p = ct.c_char_p(''.encode())
        support_ic_state = self.SysHXCTA.DLL_SupportIC(ic_list_p)
        logging.info("get_version_support_ic support_ic_state=%s", support_ic_state)
        logging.info("get_version_support_ic DLL_SupportIC=%s", ic_list_p.value.decode())

    def get_DGCTable(self, device_no, input_R, input_G, input_B, input_grey_x, input_grey_y, input_grey_Y, gray_level):
        # if device_no in self.x_dict.keys():
        #     self.x = self.x_dict[device_no]
        # if device_no in self.y_dict.keys():
        #     self.y = self.y_dict[device_no]
        # if device_no in self.y_dw_limit_dict.keys():
        #     self.y_dw_limit = self.y_dw_limit_dict[device_no]
        # if device_no in self.y_up_limit_dict.keys():
        #     self.y_up_limit = self.y_up_limit_dict[device_no]
        if self.mcu_type == "HX82115A":
            ErrMsg_p = ct.c_char_p(''.encode())
            Y_lose_flag = ct.byref(ct.c_bool(False))
            R_STR_p = ct.create_string_buffer(256)
            G_STR_p = ct.create_string_buffer(256)
            B_STR_p = ct.create_string_buffer(256)
            ChipID_p = ct.c_char_p('HX82115-A'.encode())
            GammaSet = ct.c_float(2.2)
            Only_Tune_Gamma = ct.c_bool(False)
            Set_xy_range_flag = ct.c_bool(False)
            Y_dw_limit = ct.c_float(self.y_dw_limit)
            Y_up_limit = ct.c_float(self.y_up_limit)
            Target_sx_dw = ct.c_float(self.x)
            Target_sx_up = ct.c_float(0.310)
            Target_sy_dw = ct.c_float(self.y)
            Target_sy_up = ct.c_float(0.325)

            Ref_Gray_sx = (ct.c_float * 1)(0.0)
            Ref_Gray_sy = (ct.c_float * 1)(0.0)
            Ref_Gray_Y = (ct.c_float * 1)(0.0)
            Ref_Gray_Len = ct.c_int(len(Ref_Gray_Y))

            Input_Level = (ct.c_int * len(gray_level))()
            for i in range(len(gray_level)):
                Input_Level[i] = gray_level[i]

            Input_Gray_sx = (ct.c_float * len(input_grey_x))()
            for i in range(len(input_grey_x)):
                Input_Gray_sx[i] = input_grey_x[i]

            Input_Gray_sy = (ct.c_float * len(input_grey_y))()
            for i in range(len(input_grey_y)):
                Input_Gray_sy[i] = input_grey_y[i]

            Input_Gray_Y = (ct.c_float * len(input_grey_Y))()
            for i in range(len(input_grey_Y)):
                Input_Gray_Y[i] = input_grey_Y[i]

            Input_Gray_Len = ct.c_int(len(Input_Gray_Y))

            Input_R_sx = (ct.c_float * 1)(input_R[0])
            Input_R_sy = (ct.c_float * 1)(input_R[1])
            Input_R_Y = (ct.c_float * 1)(input_R[2])
            Input_R_Len = ct.c_int(len(Input_R_Y))

            Input_G_sx = (ct.c_float * 1)(input_G[0])
            Input_G_sy = (ct.c_float * 1)(input_G[1])
            Input_G_Y = (ct.c_float * 1)(input_G[2])
            Input_G_Len = ct.c_int(len(Input_G_Y))

            Input_B_sx = (ct.c_float * 1)(input_B[0])
            Input_B_sy = (ct.c_float * 1)(input_B[1])
            Input_B_Y = (ct.c_float * 1)(input_B[2])
            Input_B_Len = ct.c_int(len(Input_B_Y))

            DGCTable_p = ct.create_string_buffer(256)
            try:
                self.SysHXCTA.CTA_Main(ErrMsg_p, Y_lose_flag, R_STR_p, G_STR_p, B_STR_p, ChipID_p, GammaSet,
                                       Only_Tune_Gamma, Set_xy_range_flag, Y_dw_limit, Y_up_limit, Target_sx_dw,
                                       Target_sx_up, Target_sy_dw, Target_sy_up, Ref_Gray_sx, Ref_Gray_sy,
                                       Ref_Gray_Y, Ref_Gray_Len, Input_Level, Input_Gray_sx, Input_Gray_sy, Input_Gray_Y,
                                       Input_Gray_Len, Input_R_sx, Input_R_sy, Input_R_Y, Input_R_Len, Input_G_sx,
                                       Input_G_sy, Input_G_Y, Input_G_Len, Input_B_sx, Input_B_sy, Input_B_Y, Input_B_Len,
                                       DGCTable_p)
            except Exception as e:
                logging.error('get_DGCTable exception:%s' % str(e.args))
            R = R_STR_p.value.decode()
            G = G_STR_p.value.decode()
            B = B_STR_p.value.decode()

            return R + G + B
        elif self.mcu_type=="HX83192C":
            R_STR = ct.create_string_buffer(4096)
            G_STR = ct.create_string_buffer(4096)
            B_STR = ct.create_string_buffer(4096)

            Input_R_Str = ct.c_char_p(B"")
            Input_G_Str = ct.c_char_p(B"")
            Input_B_Str = ct.c_char_p(B"")
            Y_dw_limit = ct.c_float(0.5)
            Y_up_limit = ct.c_float(1)

            Y_lose_flag = ct.byref(ct.c_bool(True))
            Set_xy_range_flag = ct.c_bool(False)

            Input_Target_dw_sx = ct.c_float(0.3012)
            Input_Target_dw_sy = ct.c_float(0.3134)
            Input_Target_up_sx = ct.c_float(0.000)
            Input_Target_up_sy = ct.c_float(0.000)

            OnlyWhitePoint_flag = ct.c_bool(False)
            GAMMA_SEL_Val = ct.c_float(2.2)

            Input_Meas_Level = (ct.c_int * len(gray_level))(*gray_level)
            Input_Gray_sx = (ct.c_float * len(input_grey_x))(*input_grey_x)
            Input_Gray_sy = (ct.c_float * len(input_grey_y))(*input_grey_y)
            Input_Gray_Y = (ct.c_float * len(input_grey_Y))(*input_grey_Y)

            Input_R_sx = (ct.c_float * len(gray_level))(input_R[0], 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                                                        0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                                                        0.0, 0.0, 0.0, 0.0, 0.0)

            Input_R_sy = (ct.c_float * len(gray_level))(input_R[1], 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                                                        0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                                                        0.0, 0.0, 0.0, 0.0, 0.0)

            Input_R_Y = (ct.c_float * len(gray_level))(input_R[2], 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                                                    0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                                                    0.0, 0.0, 0.0, 0.0, 0.0)

            Input_G_sx = (ct.c_float * len(gray_level))(input_G[0], 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                                                        0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                                                        0.0, 0.0, 0.0, 0.0, 0.0)

            Input_G_sy = (ct.c_float * len(gray_level))(input_G[1], 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                                                        0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                                                        0.0, 0.0, 0.0, 0.0, 0.0)

            Input_G_Y = (ct.c_float * len(gray_level))(input_G[2], 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                                                    0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                                                    0.0, 0.0, 0.0, 0.0, 0.0)

            Input_B_sx = (ct.c_float * len(gray_level))(input_B[0], 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                                                        0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                                                        0.0, 0.0, 0.0, 0.0, 0.0)

            Input_B_sy = (ct.c_float * len(gray_level))(input_B[1], 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                                                        0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                                                        0.0, 0.0, 0.0, 0.0, 0.0)

            Input_B_Y = (ct.c_float * len(gray_level))(input_B[2], 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                                                    0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                                                    0.0, 0.0, 0.0, 0.0, 0.0)

            Input_Ref_Gray_sx = (ct.c_float * 1)(0.0)
            Input_Ref_Gray_sy = (ct.c_float * 1)(0.0)
            Input_Ref_Gray_Y = (ct.c_float * 1)(0.0)

            hex_str = "18 4C 50 4F 4A 69 5C 60 61 87 3F 17 4A 4F 4F 52 6D 5C 62 63 89 3F"
            bytes_data = bytes.fromhex(hex_str.replace(" ", ""))
            Analog_PA_String = ct.c_char_p(b"18 4C 50 4F 4A 69 5C 60 61 87 3F 17 4A 4F 4F 52 6D 5C 62 63 89 3F")

            Chip_Sel_Str = ct.c_char_p(b"HX83192-C")
            LCD_Type = ct.c_char_p(b"Black")

            Flag_RQT_Min_Voltage = ct.c_bool(False)
            P_Voltage_Limit = ct.c_float(3.6)
            N_Voltage_Limit = ct.c_float(-3.6)
            VSPR = ct.c_float(5.0)
            VGSP = ct.c_float(0.0)
            VSNR = ct.c_float(-5.0)
            VGSN = ct.c_float(-0.2)

            result = ct.create_string_buffer(0x600)

            try:
                self.SysHXCTA.DGCCTA_S(R_STR, G_STR, B_STR,
                                    GAMMA_SEL_Val,
                                    Input_Target_dw_sx, Input_Target_dw_sy,
                                    Input_Target_up_sx, Input_Target_up_sy,
                                    Set_xy_range_flag,
                                    OnlyWhitePoint_flag,
                                    Input_Meas_Level,
                                    Input_Gray_sx, Input_Gray_sy, Input_Gray_Y,
                                    Input_R_sx, Input_R_sy, Input_R_Y,
                                    Input_G_sx, Input_G_sy, Input_G_Y,
                                    Input_B_sx, Input_B_sy, Input_B_Y,
                                    Input_Ref_Gray_sx, Input_Ref_Gray_sy, Input_Ref_Gray_Y,
                                    Input_R_Str, Input_G_Str, Input_B_Str,
                                    Chip_Sel_Str, LCD_Type,
                                    Y_dw_limit, Y_up_limit, Y_lose_flag,
                                    Flag_RQT_Min_Voltage,
                                    P_Voltage_Limit, N_Voltage_Limit,
                                    Analog_PA_String, VSPR, VGSP, VSNR, VGSN, result, 30)
            except Exception as e:
                logging.error('get_DGCTable exception:%s' % str(e.args))

            R_STR_value = R_STR.value.decode()
            G_STR_value = G_STR.value.decode()
            B_STR_value = B_STR.value.decode()
            logging.info('get_DGCTable R_STR_value=%s' % R_STR_value)
            logging.info('get_DGCTable G_STR_value=%s' % G_STR_value)
            logging.info('get_DGCTable B_STR_value=%s' % B_STR_value)
            result_value = result.raw
            logging.info(f"get_DGCTable result_value={result_value}")
            logging.info(f"get_DGCTable result_value_size={len(result_value)}")
            res_hex_string = ' '.join(f'{b:02x}' for b in result_value)
            return res_hex_string
        else:
            pass




SysHXCTA_manager_x64: SysHXCTAManager = SysHXCTAManager()

if __name__ == '__main__':
    SysHXCTA_manager_x64.load_dll("HX83192C")
    data = SysHXCTA_manager_x64.get_DGCTable("HW-ICSCN24-T-0001",
                                             [0.6632, 0.2972, 86.4409],
                                             [0.2894, 0.6673, 355.5500],
                                             [0.1450, 0.0636, 43.5258],
                                             [0.2962, 0.2974, 0.2975, 0.2974, 0.2973, 0.2970, 0.2967, 0.2964, 0.2960,
                                              0.2956, 0.2952, 0.2948, 0.2943, 0.2938, 0.2933, 0.2929, 0.2924, 0.2920,
                                              0.2916, 0.2912, 0.2910, 0.2908, 0.2906, 0.2905, 0.2903, 0.2900, 0.2894,
                                              0.2881, 0.2854, 0.2753, 0.2483, 0.2326],
                                             [0.3221, 0.3245, 0.3252, 0.3253, 0.3252, 0.3251, 0.3249, 0.3246, 0.3242,
                                              0.3238, 0.3235, 0.3230, 0.3226, 0.3220, 0.3216, 0.3211, 0.3206, 0.3202,
                                              0.3197, 0.3194, 0.3189, 0.3187, 0.3184, 0.3181, 0.3178, 0.3172, 0.3159,
                                              0.3134, 0.3088, 0.2921, 0.2481, 0.2216],
                                             [482.5396, 451.3436, 417.2518, 384.7057, 353.2667, 326.9835, 297.2894,
                                              268.6550, 242.9449, 221.1727, 198.8361, 177.8659, 157.3409, 138.3899,
                                              122.1677, 106.7292, 92.3493, 79.0386, 66.5624, 55.5818, 45.7769, 37.2401,
                                              29.8748, 22.8023, 17.1690, 12.5369, 8.7507, 5.7417, 3.5059, 1.4475,
                                              0.5053,
                                              0.01],
                                             [255, 248, 240, 232, 224, 216, 208, 200, 192, 184, 176, 168, 160, 152, 144,
                                              136, 128, 120, 112, 104, 96, 88, 80, 72, 64, 56, 48, 40, 32, 22, 10, 0])
    print(data, len(data))
