<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1000</width>
    <height>600</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1000</width>
    <height>600</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>产品自动化测试平台</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout_4">
    <item>
     <layout class="QVBoxLayout" name="verticalLayout_3">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>20</number>
      </property>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,0,0,0,0,0,0,0,0,0,0,0,0">
        <property name="spacing">
         <number>6</number>
        </property>
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>5</number>
        </property>
        <item>
         <spacer name="horizontalSpacer_2">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Fixed</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>25</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_5">
          <property name="spacing">
           <number>0</number>
          </property>
          <item>
           <widget class="QPushButton" name="test_plan_btn">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>50</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>12</pointsize>
              <weight>50</weight>
              <bold>false</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <property name="text">
             <string>测试计划</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QFrame" name="frame">
            <property name="minimumSize">
             <size>
              <width>150</width>
              <height>20</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">border:none</string>
            </property>
            <property name="frameShape">
             <enum>QFrame::StyledPanel</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <layout class="QHBoxLayout" name="horizontalLayout_4">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QLabel" name="test_plan_label">
               <property name="minimumSize">
                <size>
                 <width>150</width>
                 <height>30</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">border:none</string>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="horizontalSpacer_3">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Fixed</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_6">
          <property name="spacing">
           <number>0</number>
          </property>
          <item>
           <widget class="QPushButton" name="project_info_btn">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>50</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>12</pointsize>
              <weight>50</weight>
              <bold>false</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <property name="text">
             <string>项目信息</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QFrame" name="frame_2">
            <property name="minimumSize">
             <size>
              <width>150</width>
              <height>20</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">border:none</string>
            </property>
            <property name="frameShape">
             <enum>QFrame::StyledPanel</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <layout class="QHBoxLayout" name="horizontalLayout_5">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QLabel" name="project_info_label">
               <property name="minimumSize">
                <size>
                 <width>150</width>
                 <height>30</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">border:none</string>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="horizontalSpacer_4">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Fixed</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_7">
          <property name="spacing">
           <number>0</number>
          </property>
          <item>
           <widget class="QPushButton" name="machine_peripherals_btn">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>50</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>12</pointsize>
              <weight>50</weight>
              <bold>false</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <property name="text">
             <string>机台外设</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QFrame" name="frame_3">
            <property name="minimumSize">
             <size>
              <width>150</width>
              <height>20</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">border:none</string>
            </property>
            <property name="frameShape">
             <enum>QFrame::StyledPanel</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <layout class="QHBoxLayout" name="horizontalLayout_6">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QLabel" name="machine_peripherals_label">
               <property name="minimumSize">
                <size>
                 <width>150</width>
                 <height>30</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">border:none</string>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="horizontalSpacer_6">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Fixed</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QPushButton" name="test_tools_btn">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>50</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>12</pointsize>
              <weight>50</weight>
              <bold>false</bold>
             </font>
            </property>
            <property name="text">
             <string>测试工具</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QFrame" name="frame_4">
            <property name="minimumSize">
             <size>
              <width>150</width>
              <height>20</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">border:none</string>
            </property>
            <property name="frameShape">
             <enum>QFrame::StyledPanel</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <layout class="QHBoxLayout" name="horizontalLayout_2">
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QLabel" name="test_tools_label">
               <property name="minimumSize">
                <size>
                 <width>150</width>
                 <height>30</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                 <weight>50</weight>
                 <bold>false</bold>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">border:none</string>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
               <property name="margin">
                <number>0</number>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="label">
          <property name="font">
           <font>
            <weight>50</weight>
            <bold>false</bold>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="text">
           <string>项目名称：</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="ExtendedComboBox" name="comboBox_project">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>600</width>
            <height>45</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>600</width>
            <height>45</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
          <property name="sizeAdjustPolicy">
           <enum>QComboBox::AdjustToContents</enum>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="refresh_project_btn">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>45</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="font">
           <font>
            <weight>50</weight>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>刷新</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_5">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Fixed</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>35</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QLabel" name="label_2">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>30</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QStackedWidget" name="stackedWidget">
        <property name="font">
         <font>
          <pointsize>12</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
       </widget>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <action name="actionSystemSetting">
   <property name="text">
    <string>系统设置</string>
   </property>
  </action>
  <action name="actionNewProject">
   <property name="text">
    <string>新建项目</string>
   </property>
   <property name="visible">
    <bool>true</bool>
   </property>
   <property name="iconVisibleInMenu">
    <bool>true</bool>
   </property>
  </action>
  <action name="actionEditProject">
   <property name="text">
    <string>编辑项目</string>
   </property>
  </action>
  <action name="actionOpenProject">
   <property name="text">
    <string>打开项目</string>
   </property>
  </action>
  <action name="actionRelayPower">
   <property name="text">
    <string>继电器</string>
   </property>
  </action>
  <action name="actionhelp_about">
   <property name="text">
    <string>帮助</string>
   </property>
  </action>
  <action name="actionProgramPower">
   <property name="text">
    <string>Tommens程控电源</string>
   </property>
  </action>
  <action name="actionMatePushImageTool">
   <property name="text">
    <string>Mate推图工具</string>
   </property>
  </action>
  <action name="actionPictureTool">
   <property name="text">
    <string>图片生成工具</string>
   </property>
  </action>
  <action name="actionAutoPress">
   <property name="text">
    <string>自动按键</string>
   </property>
  </action>
  <action name="actionIoControl">
   <property name="text">
    <string>板卡运动控制工具</string>
   </property>
  </action>
  <action name="actionProjectSetting">
   <property name="text">
    <string>项目设置</string>
   </property>
  </action>
  <action name="actionMachineInit">
   <property name="text">
    <string>机台初始化</string>
   </property>
  </action>
  <action name="actionMachineStart">
   <property name="text">
    <string>机台开始</string>
   </property>
  </action>
  <action name="actionMachineReset">
   <property name="text">
    <string>机台复位</string>
   </property>
  </action>
  <action name="actionMachineStop">
   <property name="text">
    <string>机台停止</string>
   </property>
  </action>
  <action name="actionPositionControl">
   <property name="text">
    <string>板卡位置控制工具</string>
   </property>
  </action>
  <action name="actionProgramPowerIT_M3200">
   <property name="text">
    <string>IT-M3200程控电源</string>
   </property>
  </action>
  <action name="action_logic">
   <property name="text">
    <string>逻辑分析仪</string>
   </property>
  </action>
  <action name="DBCMessageSend">
   <property name="text">
    <string>DBC发送工具</string>
   </property>
  </action>
  <action name="DBCMessageRecv">
   <property name="text">
    <string>DBC接收工具</string>
   </property>
  </action>
  <action name="actionTriColorLight">
   <property name="text">
    <string>三色灯</string>
   </property>
  </action>
  <action name="actionExportProject">
   <property name="text">
    <string>导出项目</string>
   </property>
  </action>
  <action name="actionPullPlan">
   <property name="text">
    <string>拉取测试计划</string>
   </property>
  </action>
 </widget>
 <customwidgets>
  <customwidget>
   <class>ExtendedComboBox</class>
   <extends>QComboBox</extends>
   <header location="global">view/ExtendedComboBox.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
