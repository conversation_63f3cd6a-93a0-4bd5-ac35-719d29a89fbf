# -*- coding: utf-8 -*-
"""
<AUTHOR> Assistant
@Date   : 2025-01-28
@Desc   : 测试优化后的日志输出
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fs_manager.FSManager import FSManager, project_manager


def test_optimized_logs():
    """测试优化后的日志输出"""
    print("=" * 60)
    print("测试优化后的 alert_device_offline 日志输出")
    print("=" * 60)
    
    # 设置固定的 Bearer Token
    fixed_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUzNzc3OTQwLCJpYXQiOjE3NTM2OTE1NDAsImp0aSI6IjczMjA4Mzk4ZGE4MTQxNDc4MGU4NWYzY2YyMGVhYjIzIiwidXNlcl9pZCI6MTgxfQ.SHUv-BmzTOfSlX-GqFVckOscEjTZJIQpUv6HnZ1HwoI"
    
    # 保存原始 token
    original_token = project_manager.get_access_token()
    
    try:
        # 设置固定 token
        project_manager.set_access_token(fixed_token)
        
        print("\n1. 测试成功获取项目测试人员的情况")
        print("-" * 40)
        params1 = {
            "project_number": "RESSN10",
            "device_name": "显示屏",
            "exception_desc": "设备离线"
        }
        result1 = FSManager.alert_device_offline(params1)
        print(f"结果: {'✅ 成功' if result1 else '❌ 失败'}")
        
        print("\n2. 测试项目不存在的情况（使用默认用户）")
        print("-" * 40)
        params2 = {
            "project_number": "NONEXISTENT",
            "device_name": "触摸屏",
            "exception_desc": "设备异常"
        }
        result2 = FSManager.alert_device_offline(params2)
        print(f"结果: {'✅ 成功' if result2 else '❌ 失败'}")
        
        print("\n3. 测试不提供项目编号的情况（使用默认用户）")
        print("-" * 40)
        params3 = {
            "device_name": "传感器",
            "exception_desc": "传感器无响应"
        }
        result3 = FSManager.alert_device_offline(params3)
        print(f"结果: {'✅ 成功' if result3 else '❌ 失败'}")
        
        print("\n" + "=" * 60)
        print("日志优化说明:")
        print("✅ 减少了冗余的日志输出")
        print("✅ 只在关键步骤记录日志")
        print("✅ 如果没有用户则不发送通知")
        print("✅ 日志信息更加简洁明了")
        
    except Exception as e:
        print(f"测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        # 恢复原始 token
        project_manager.set_access_token(original_token)
        print("\n已恢复原始 token")


if __name__ == '__main__':
    test_optimized_logs()
