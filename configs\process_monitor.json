{"ip": "127.0.0.1", "port": 30000, "config": {"ICSCN25": {"2B1": 1, "2B2": 1, "2A:": 0, "2C": 100, "ERROR_LIST": ["亮度故障", "TCON故障", "电压故障", "触摸故障", "息屏故障", "pmicerr故障", "bridgeerr故障", "温度异常"], "error_info": [[], [], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0, 0], "FlagDict": {"ics_warningrsp_thi": 7, "ics_warningrsp_brilimt": 0, "ics_warningrsp_tconerr": 1, "ics_warningrsp_uhilo": 2, "ics_warningrsp_toucherr": 3, "ics_warningrsp_locksts": 4, "ics_warningrsp_pmicerr": 5, "ics_warningrsp_bridgeerr": 6}}, "ICSCN38": {"2B1": 1, "2B2": 1, "2A:": 0, "2C": 100, "ERROR_LIST": ["亮度故障", "TCON故障", "电压故障", "触摸故障", "息屏故障", "pmicerr故障", "bridgeerr故障", "温度异常"], "error_info": [[], [], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0, 0], "FlagDict": {"ics_warningrsp_thi": 7, "ics_warningrsp_brilimt": 0, "ics_warningrsp_tconerr": 1, "ics_warningrsp_uhilo": 2, "ics_warningrsp_toucherr": 3, "ics_warningrsp_locksts": 4, "ics_warningrsp_pmicerr": 5, "ics_warningrsp_bridgeerr": 6}}, "NT2.0IC": {"2B1": 1, "2B2": 1, "2A:": 0, "2C": 100, "ERROR_LIST": ["电压故障", "LCD故障", "Tcon故障", "亮度故障", "温度异常", "leddrvr故障", "息屏故障"], "error_info": [[], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0], "FlagDict": {"ic_warningrsp_uhilo": 0, "ic_warningrsp_lcdfail": 1, "ic_warningrsp_tconfail": 2, "ic_warningrsp_brilimt": 3, "ic_warningrsp_thi": 4, "ic_warningrsp_leddrvr": 5, "ic_warningrsp_locksts": 6}}, "LCMCN11": {"2B1": 1, "2B2": 1, "2A:": 0, "2C": 100, "ERROR_LIST": ["电压故障", "LCD故障", "Tcon故障", "亮度故障", "温度异常", "leddrvr故障", "息屏故障"], "error_info": [[], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0], "FlagDict": {"ic_warningrsp_uhilo": 0, "ic_warningrsp_lcdfail": 1, "ic_warningrsp_tconfail": 2, "ic_warningrsp_brilimt": 3, "ic_warningrsp_thi": 4, "ic_warningrsp_leddrvr": 5, "ic_warningrsp_locksts": 6}}, "NT2.0FIR": {"2B1": 1, "2B2": 1, "2A:": 0, "2C": 100, "ERROR_LIST": ["亮度故障", "LCD故障", "leddrvr故障", "Tcon故障", "息屏故障", "温度异常", "电压故障", "触摸故障"], "error_info": [[], [], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0, 0], "FlagDict": {"fi_r_warningrsp_brilimt": 0, "fi_r_warningrsp_lcd": 1, "fi_r_warningrsp_leddrvr": 2, "fi_r_warningrsp_tconfail": 3, "fi_r_warningrsp_locksts": 4, "fi_r_warningrsp_thi": 5, "fi_r_warningrsp_uhilo": 6, "fi_r_warningrsp_toucherr": 7}}, "ICSCN37": {"2B1": 10, "2B2": 10, "2A:": 0, "2C": 100, "ERROR_LIST": ["亮度故障", "电压故障", "OLED故障", "温度异常", "息屏故障", "TCON故障", "触摸故障"], "error_info": [[], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0], "FlagDict": {"ics_warning_brilimt": 0, "ics_warning_uhilo": 1, "ics_warning_oledfail": 2, "ics_warning_thi": 3, "ics_warning_locksts": 4, "ics_warning_tcon": 5, "ics_warning_toucherr": 6}}, "MSNCN15": {"2B1": 1, "2B2": 1, "2A:": 0, "2C": 100, "ERROR_LIST": ["亮度故障-L", "亮度故障-R", "息屏故障", "温度异常-L", "温度异常-R", "电压故障", "tcon故障-L", "tcon故障-R", "rohm故障-L", "rohm故障-R"], "error_info": [[], [], [], [], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "FlagDict": {"ic_warningrsp_brilimt_l": 0, "ic_warningrsp_brilimt_r": 1, "ic_warningrsp_locksts": 2, "ic_warningrsp_thi_l": 3, "ic_warningrsp_thi_r": 4, "ic_warningrsp_uhilo_l": 5, "ic_warningrsp_tconfail_l": 6, "ic_warningrsp_tconfail_r": 7, "ic_warningrsp_rohmfail_l": 8, "ic_warningrsp_rohmfail_r": 9}}, "ALPS_DOM_FIR": {"2B1": 1, "2B2": 10, "2A:": 0, "2C": 100, "ERROR_LIST": ["sts故障", "brightnesssts故障", "息屏故障", "温度异常", "Touch故障", "电压故障", "tcon故障", "pmicerr故障", "bridgeerr故障"], "error_info": [[], [], [], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0, 0, 0], "FlagDict": {"fi_r_sts": 0, "fi_r_warningrsp_brilimt": 1, "fi_r_warningrsp_locksts": 2, "fi_r_warningrsp_thi": 3, "fi_r_warningrsp_toucherr": 4, "fi_r_warningrsp_uhilo": 5, "fi_r_warningrsp_tconerr": 6, "fi_r_warningrsp_pmicerr": 7, "fi_r_warningrsp_bridgeerr": 8}}, "ICSCN22": {"2B1": 1, "2B2": 0, "2A:": 0, "2C": 100, "ERROR_LIST": ["锁舌故障", "电机状态故障", "右侧锁舌位置故障", "亮度受限状态故障", "lock故障", "PMIC故障", "TCON IC故障", "TDDI故障", "高温故障", "触摸故障", "高低压故障", "背光控制IC故障", "电机故障信息1", "电机故障信息2", "电机故障信息3"], "error_info": [[], [], [], [], [], [], [], [], [], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "FlagDict": {"res_motor_leftlockpos": 0, "res_motor_resmotorsts": 1, "res_motor_rightlockpos": 2, "res_waringrsp_brilimt": 3, "res_warning_locksts": 4, "res_warning_pmicerr": 5, "res_warning_tconerr": 6, "res_warning_tddierr": 7, "res_warning_thi": 8, "res_warning_toucherr": 9, "res_warning_uhilo": 10, "res_bl_error": 11, "res_warning_errorcode1": 12, "res_warning_errorcode2": 13, "res_warning_errorcode3": 14}}, "RESCN12": {"2B1": 1, "2B2": 1, "2A:": 0, "2C": 100, "ERROR_LIST": ["Oldi/EdpError", "DisplayError", "显示临时关闭", "门/源驱动错误", "看门狗或MCU软件复位错", "异常上电复位", "Pmic电源管理IC错误", "LVDS链路锁定错误", "背光错误", "背光LED故障", "背光过电压", "背光欠电压", "背光过电流", "背光温度高", "背光温度低", "过去发生过背光错误", "SysVoltageValue", "NtcTempValue", "TemStatusValue", "BacklightStatusValue", "BacklightLevelValue"], "error_info": [[], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "FlagDict": {"oldi_edp": 0, "display": 1, "display_temp_shutoff": 2, "gate_source_driver": 3, "watchdog_mcu_reset": 4, "abnormal_poweron_reset": 5, "pmic": 6, "lvds_link": 7, "backlight_error": 8, "backlight_led": 9, "backlight_overvoltage": 10, "backlight_undervoltage": 11, "backlight_overcurrent": 12, "backlight_temp_high": 13, "backlight_temp_low": 14, "backlight_past_error": 15, "system_voltage_value": 16, "ntc_temperature_value": 17, "temperature_status_value": 18, "backlight_status_value": 19, "backlight_level_value": 20}}, "MSNCN19": {"2B1": 1, "2B2": 1, "2A:": 0, "2C": 100, "ERROR_LIST": ["右屏高温故障", "LOCK信号状态", "左屏高温故障", "左屏亮度受限状态", "ic_warningrsp_tcon_source_l", "ic_warningrsp_bridge_l", "左屏LED灯驱动故障", "左屏高低压故障", "右屏亮度受限状态", "ic_warningrsp_tcon_source_r", "ic_warningrsp_bridge_r", "右屏LED灯驱动故障", "右屏高低压故障", "系统状态", "ic_power_fail", "ic_tconfaultsts_0x0203", "ic_bridgefaultsts_0x1d20", "ic_bridgefaultsts_0x1d21", "ic_bridgefaultsts_0x1d22", "ic_bridgefaultsts_0x1d23", "ic_fusafaultsts_1", "ic_fusafaultsts_2", "ic_fusafaultsts_3"], "error_info": [[], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "FlagDict": {"ic_warningrsp_thi_r": 0, "ic_warningrsp_locksts": 1, "ic_warningrsp_thi_l": 2, "ic_warningrsp_brilimt_l": 3, "ic_warningrsp_tcon_source_l": 4, "ic_warningrsp_bridge_l": 5, "ic_warningrsp_leddrvr_l": 6, "ic_warningrsp_uhilo_l": 7, "ic_warningrsp_brilimt_r": 8, "ic_warningrsp_tcon_source_r": 9, "ic_warningrsp_bridge_r": 10, "ic_warningrsp_leddrvr_r": 11, "ic_warningrsp_uhilo_r": 12, "ic_sts": 13, "ic_power_fail": 14, "ic_tconfaultsts_0x0203": 15, "ic_bridgefaultsts_0x1d20": 16, "ic_bridgefaultsts_0x1d21": 17, "ic_bridgefaultsts_0x1d22": 18, "ic_bridgefaultsts_0x1d23": 19, "ic_fusafaultsts_1": 20, "ic_fusafaultsts_2": 21, "ic_fusafaultsts_3": 22}}, "RESCN11": {"2B1": 1, "2B2": 0, "2A:": 0, "2C": 100, "ERROR_LIST": ["锁舌故障", "电机状态故障", "右侧锁舌位置故障", "亮度受限状态故障", "lock故障", "PMIC故障", "TCON IC故障", "TDDI故障", "高温故障", "触摸故障", "高低压故障", "背光控制IC故障", "电机故障信息1", "电机故障信息2", "电机故障信息3"], "error_info": [[], [], [], [], [], [], [], [], [], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "FlagDict": {"res_motor_leftlockpos": 0, "res_motor_resmotorsts": 1, "res_motor_rightlockpos": 2, "res_waringrsp_brilimt": 3, "res_warning_locksts": 4, "res_warning_pmicerr": 5, "res_warning_tconerr": 6, "res_warning_tddierr": 7, "res_warning_thi": 8, "res_warning_toucherr": 9, "res_warning_uhilo": 10, "res_bl_error": 11, "res_warning_errorcode1": 12, "res_warning_errorcode2": 13, "res_warning_errorcode3": 14}}, "RESCN14": {"2B1": 1, "2B2": 0, "2A:": 0, "2C": 100, "ERROR_LIST": ["主电机堵转", "过流异常", "过速度异常", "过载异常", "电机过温异常", "缺相", "编码器通信异常", "电机同步异常", "电机齿隙异常", "屏零位未校准", "逆变电路异常", "过温异常", "锁舌异常", "显示角度异常", "夹手故障", "驱动IC故障", "高温异常", "背光异常", "bridgeIC异常", "tddi异常", "pmic异常", "usb通信异常", "显示屏触摸异常", "串口通信异常", "温度NTC异常", "高低压异常", "电压NTC异常"], "error_info": [[], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "FlagDict": {"motor_stall": 0, "motor_overcurrent": 1, "motor_overspeed ": 2, "motor_overload ": 3, "motor_high_temperature": 4, "lost_phase": 5, "motor_encoder_fault": 6, "motor_synchronous_error": 7, "abnormal_motor_backlash": 8, "screen_uncalibrated": 9, "abnormal_current_sampling_bias": 10, "pcba_high_temperature_pcba": 11, "lock_fault": 12, "abnormal_angle_sensor": 13, "clamp_hand": 14, "motor_driver_ic": 15, "temperature_over_threshold": 16, "backlight_fault": 17, "bridge_ic_fault": 18, "tddi_falut": 19, "pmic_fault": 20, "usb_communication_error": 21, "touch_fault": 22, "abnormal_uart_communication": 23, "temperature_ntc_abnormality": 24, "high_low_voltage_fault": 25, "voltage_ntc_fault": 26}}, "RESSN009": {"2B1": 1, "2B2": 0, "2A:": 0, "2C": 100, "ERROR_LIST": ["触摸故障状态", "高低压故障状态", "高温故障状态", "吸顶屏锁故障状态", "高温亮度限制状态", "触控与显示芯片故障状态", "吸顶屏屏幕状态", "吸顶屏背光亮度", "右锁舌状态", "左锁舌状态", "吸顶屏电机温度", "吸顶屏电机电压", "吸顶屏电机电流", "吸顶屏电机展开角度", "吸顶屏电机开闭速度状态", "吸顶屏主轴电机编码故障", "吸顶屏电机高压故障", "吸顶屏电机低压故障", "吸顶屏逆变电路故障", "吸顶屏电机高温故障", "吸顶屏锁舌故障", "吸顶屏磁编码器故障", "吸顶屏夹手故障", "吸顶屏主轴电机堵转故障", "吸顶屏主轴电机过流故障", "吸顶屏主轴电机超速故障", "吸顶屏主轴电机过载故障", "吸顶屏主轴电机过温故障", "吸顶屏主轴电机缺相故障"], "error_info": [[], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "FlagDict": {"cs_tcherrsts": 0, "cs_horlvlterrsts": 1, "cs_htemperrsts": 2, "cs_lockerrsts": 3, "cs_htempbrtnslmtsts": 4, "cs_stddierrsts": 5, "cs_scrsts": 6, "cs_bcklgbrtns_value": 7, "cs_rlockboltsts": 8, "cs_llockboltsts": 9, "cs_tmtemp_value": 10, "cs_tmvlt_value": 11, "cs_tmcur_value": 12, "cs_tmuflodang_value": 13, "cs_tmopencls_spdsts": 14, "cs_motoendcdflt": 15, "cs_pcbahivolfit": 16, "cs_pcbalovolfit": 17, "cs_invcirfit": 18, "cs_pcbahitempfit": 19, "cs_locktogufit": 20, "cs_magcencdfit": 21, "cs_clphandfit": 22, "cs_motorstalfit": 23, "cs_motorovercurrentfit": 24, "cs_motoroverspdfit": 25, "cs_motoroverloadfit": 26, "cs_motorhitempfit": 27, "cs_motorphslossfit": 28}}, "ACSCN11": {"2B1": 1, "2B2": 0, "2A:": 0, "2C": 100, "ERROR_LIST": ["显示屏状态故障", "显示屏背光温度状态故障", "显示屏整体功能状态故障", "显示功能状态故障", "触摸功能状态故障", "背光功能状态故障", "视频信号状态故障", "电池过压状态故障", "电池欠压状态故障"], "error_info": [[], [], [], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0, 0, 0], "FlagDict": {"disp_status": 0, "temp_status": 1, "summay": 2, "display": 3, "touch": 4, "bl": 5, "lockfail": 6, "status_ovp": 7, "status_lvp": 8}}}, "DEBUG": 0}