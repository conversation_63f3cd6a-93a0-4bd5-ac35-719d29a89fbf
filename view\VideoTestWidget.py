import datetime
import operator
import os
import threading

from PyQt5.QtCore import QTimer, QTime, Qt
from PyQt5.QtWidgets import QWidget, QGraphicsPixmapItem, QGraphicsScene, QListView, QFileDialog

from common.AppConfig import app_config
from common.LogUtils import logger
from common.view.MessageDialog import MessageDialog
from ui.VideoTest import Ui_video_cap
from utils.SignalsManager import signals_manager
from vision.CameraConfig import camera_config
from vision.FrequencyTest import frequency_test
from vision.VideoDetectManager import video_detect_manager


class VideoTestWidget(QWidget, Ui_video_cap):

    def __init__(self):
        super().__init__()
        self.setupUi(self)
        self.setWindowTitle("视频测试工具")
        self.scene = QGraphicsScene()
        self.image_item = QGraphicsPixmapItem()
        self.scene.addItem(self.image_item)
        self.graphicsView.setScene(self.scene)
        self.scene_change.addItems(["闪屏测试", "花屏测试"])
        self.load_video.clicked.connect(self.show_file_dialog)
        self.start_video_test_button.clicked.connect(self.start_video_test)
        self.open_video_path = ""
        self.elapsed_time = QTime(0, 0)
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_timer)
        self.selected_index = 0
        self.scene_change.setCursor(Qt.PointingHandCursor)
        self.load_video.setCursor(Qt.PointingHandCursor)
        self.start_video_test_button.setCursor(Qt.PointingHandCursor)
        signals_manager.video_test_finish.connect(self.update_test_status)
        signals_manager.collect_video_changed.connect(self.show_graphics_video)

    def update_test_status(self, detect_type, msg):
        logger.info(f"update_test_status msg={msg}")
        self.test_status_label.setText("测试完成")
        self.start_video_test_button.setEnabled(True)
        if operator.eq("闪屏测试", detect_type):
            self.detect_value_textedit.setText(str(video_detect_manager.flicker_values))
        elif operator.eq("花屏测试", detect_type):
            self.detect_value_textedit.setText(str(video_detect_manager.grainy_values))
        MessageDialog.show_message("提示", msg)

    def show_file_dialog(self):
        logger.info(f"show_file_dialog")
        # 弹出文件选择对话框
        file_dialog = QFileDialog()
        file_dialog.setNameFilter('Images (*.mp4)')  # 设置文件过滤器
        file_path, _ = file_dialog.getOpenFileNames(self, '选择文件', "", 'Images (*.mp4)')

        # 输出所选文件的路径（如果有选择的话）
        if file_path:
            self.open_video_path = file_path

    def start_video_test(self):
        logger.info(f"start_video_test")
        if operator.eq("", self.open_video_path):
            return

        self.detect_value_textedit.clear()
        date = f"{datetime.datetime.now().strftime('%Y-%m-%d')}"
        base_path = os.path.join(app_config.vision_folder, date)
        camera_config.set_resource_path(base_path)
        selected_scene = self.scene_change.currentText().strip()
        camera_config.flicker_threshold = float(self.flicker_threshold_lineedit.text())
        camera_config.grainy_threshold = float(self.grainy_threshold_lineedit.text())
        if operator.eq("闪屏测试", selected_scene):
            threading.Thread(target=frequency_test.start_video_test_flicker, args=(self.open_video_path,)).start()
        elif operator.eq("花屏测试", selected_scene):
            threading.Thread(target=frequency_test.start_video_test_grainy, args=(self.open_video_path,)).start()
        self.start_video_test_button.setEnabled(False)
        self.test_status_label.setText('测试中')

    def show_graphics_video(self, image):
        pixmap_item = QGraphicsPixmapItem(image)
        self.scene.clear()
        self.scene.addItem(pixmap_item)

    def start_timer(self):
        self.elapsed_time = QTime(0, 0)
        self.timer.start(1000)  # 1秒的定时器间隔

    def update_timer(self):
        self.elapsed_time = self.elapsed_time.addSecs(1)

    def stop_timer(self):
        self.timer.stop()
