# -*- coding: utf-8 -*-
"""
<AUTHOR> Assistant
@Date   : 2025-01-23
@Desc   : 简化版测试 FSManager.get_tester 方法（不依赖网络）
"""

import sys
import os
import unittest
from unittest.mock import patch, Mock, MagicMock

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Mock requests 模块
sys.modules['requests'] = MagicMock()
sys.modules['requests.exceptions'] = MagicMock()

# Mock 其他依赖模块
sys.modules['common.LogUtils'] = MagicMock()
sys.modules['utils.SignalsManager'] = MagicMock()
sys.modules['utils.ProjectManager'] = MagicMock()

# 现在可以安全导入 FSManager
from fs_manager.FSManager import FSManager


class TestGetTesterSimple(unittest.TestCase):
    """简化版测试 FSManager.get_tester 方法"""

    def setUp(self):
        """测试前的准备工作"""
        self.test_project_number = "RESSN10"

    @patch('fs_manager.FSManager.project_manager')
    @patch('fs_manager.FSManager.requests')
    def test_get_tester_success(self, mock_requests, mock_project_manager):
        """测试成功获取测试人员信息"""
        # 模拟 project_manager.get_access_token() 返回值
        mock_project_manager.get_access_token.return_value = "test_token"
        
        # 模拟成功的 API 响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "err_code": 0,
            "msg": "success",
            "data": {
                "testers": [
                    {"id": 1, "name": "张三", "email": "<EMAIL>"},
                    {"id": 2, "name": "李四", "email": "<EMAIL>"}
                ]
            }
        }
        mock_requests.get.return_value = mock_response
        
        # 调用方法
        status, data, err_msg = FSManager.get_tester(self.test_project_number)
        
        # 验证结果
        self.assertTrue(status)
        self.assertIsNotNone(data)
        self.assertEqual(err_msg, "")
        self.assertIn("testers", data)
        self.assertEqual(len(data["testers"]), 2)
        
        # 验证调用参数
        mock_requests.get.assert_called_once()
        call_args = mock_requests.get.call_args
        self.assertIn("params", call_args.kwargs)
        self.assertEqual(call_args.kwargs["params"]["project_number"], self.test_project_number)

    @patch('fs_manager.FSManager.project_manager')
    @patch('fs_manager.FSManager.requests')
    def test_get_tester_api_error(self, mock_requests, mock_project_manager):
        """测试 API 返回错误"""
        # 模拟 project_manager.get_access_token() 返回值
        mock_project_manager.get_access_token.return_value = "test_token"
        
        # 模拟 API 错误响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "err_code": 1001,
            "msg": "项目不存在",
            "data": None
        }
        mock_requests.get.return_value = mock_response
        
        # 调用方法
        status, data, err_msg = FSManager.get_tester(self.test_project_number)
        
        # 验证结果
        self.assertFalse(status)
        self.assertIsNone(data)
        self.assertIn("项目不存在", err_msg)

    @patch('fs_manager.FSManager.project_manager')
    @patch('fs_manager.FSManager.requests')
    def test_get_tester_400_error(self, mock_requests, mock_project_manager):
        """测试 400 错误（请求参数错误）"""
        # 模拟 project_manager.get_access_token() 返回值
        mock_project_manager.get_access_token.return_value = "test_token"
        
        # 模拟 400 错误响应
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.json.return_value = {
            "msg": "项目编号不能为空"
        }
        mock_requests.get.return_value = mock_response
        
        # 调用方法
        status, data, err_msg = FSManager.get_tester("")
        
        # 验证结果
        self.assertFalse(status)
        self.assertIsNone(data)
        self.assertIn("请求参数错误", err_msg)
        self.assertIn("项目编号不能为空", err_msg)

    @patch('fs_manager.FSManager.project_manager')
    @patch('fs_manager.FSManager.requests')
    def test_get_tester_401_error(self, mock_requests, mock_project_manager):
        """测试 401 错误（认证失败）"""
        # 模拟 project_manager.get_access_token() 返回值
        mock_project_manager.get_access_token.return_value = "invalid_token"
        
        # 模拟 401 错误响应
        mock_response = Mock()
        mock_response.status_code = 401
        mock_response.json.return_value = {
            "code": "UNAUTHORIZED",
            "detail": "Token 无效"
        }
        mock_requests.get.return_value = mock_response
        
        # 调用方法
        status, data, err_msg = FSManager.get_tester(self.test_project_number)
        
        # 验证结果
        self.assertFalse(status)
        self.assertIsNone(data)
        self.assertIn("认证失败", err_msg)

    @patch('fs_manager.FSManager.project_manager')
    @patch('fs_manager.FSManager.requests')
    def test_get_tester_timeout(self, mock_requests, mock_project_manager):
        """测试请求超时"""
        # 模拟 project_manager.get_access_token() 返回值
        mock_project_manager.get_access_token.return_value = "test_token"
        
        # 创建 Timeout 异常类
        class TimeoutException(Exception):
            pass

        # 模拟超时异常
        mock_requests.exceptions.Timeout = TimeoutException
        mock_requests.get.side_effect = TimeoutException()
        
        # 调用方法
        status, data, err_msg = FSManager.get_tester(self.test_project_number)
        
        # 验证结果
        self.assertFalse(status)
        self.assertIsNone(data)
        self.assertIn("请求超时", err_msg)

    @patch('fs_manager.FSManager.project_manager')
    @patch('fs_manager.FSManager.requests')
    def test_get_tester_connection_error(self, mock_requests, mock_project_manager):
        """测试网络连接错误"""
        # 模拟 project_manager.get_access_token() 返回值
        mock_project_manager.get_access_token.return_value = "test_token"
        
        # 创建 ConnectionError 异常类
        class ConnectionErrorException(Exception):
            pass

        # 模拟连接错误
        mock_requests.exceptions.ConnectionError = ConnectionErrorException
        mock_requests.get.side_effect = ConnectionErrorException()
        
        # 调用方法
        status, data, err_msg = FSManager.get_tester(self.test_project_number)
        
        # 验证结果
        self.assertFalse(status)
        self.assertIsNone(data)
        self.assertIn("网络连接失败", err_msg)

    def test_method_signature(self):
        """测试方法签名"""
        # 验证方法存在
        self.assertTrue(hasattr(FSManager, 'get_tester'))
        
        # 验证是静态方法
        self.assertTrue(callable(getattr(FSManager, 'get_tester')))

    def test_url_construction(self):
        """测试 URL 构造"""
        from fs_manager import Url, get_tester
        
        expected_url = Url.base_url + get_tester
        self.assertEqual(expected_url, "http://10.1.1.131:9000/auto_test/get_tester")

    @patch('fs_manager.FSManager.project_manager')
    @patch('fs_manager.FSManager.requests')
    def test_headers_construction(self, mock_requests, mock_project_manager):
        """测试请求头构造"""
        # 模拟 project_manager.get_access_token() 返回值
        test_token = "test_access_token_123"
        mock_project_manager.get_access_token.return_value = test_token
        
        # 模拟成功响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"err_code": 0, "data": {}}
        mock_requests.get.return_value = mock_response
        
        # 调用方法
        FSManager.get_tester(self.test_project_number)
        
        # 验证请求头
        call_args = mock_requests.get.call_args
        headers = call_args.kwargs.get("headers", {})
        
        self.assertIn("Authorization", headers)
        self.assertEqual(headers["Authorization"], f"Bearer {test_token}")
        self.assertIn("User-Agent", headers)

    def test_different_project_numbers(self):
        """测试不同的项目编号格式"""
        test_cases = [
            "RESSN10",
            "ABC123", 
            "TEST_PROJECT_001",
            "项目编号中文",
            ""
        ]
        
        with patch('fs_manager.FSManager.project_manager') as mock_project_manager, \
             patch('fs_manager.FSManager.requests') as mock_requests:
            
            mock_project_manager.get_access_token.return_value = "test_token"
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"err_code": 0, "data": {"testers": []}}
            mock_requests.get.return_value = mock_response
            
            for project_number in test_cases:
                with self.subTest(project_number=project_number):
                    status, data, err_msg = FSManager.get_tester(project_number)
                    
                    # 验证调用参数
                    call_args = mock_requests.get.call_args
                    self.assertEqual(
                        call_args.kwargs["params"]["project_number"], 
                        project_number
                    )


def run_manual_test():
    """手动测试函数，用于演示方法调用"""
    print("=" * 50)
    print("手动测试演示 FSManager.get_tester 方法")
    print("=" * 50)
    
    # 测试项目编号
    test_project_number = "RESSN10"
    
    print(f"测试项目编号: {test_project_number}")
    print("-" * 30)
    
    print("注意：由于网络环境限制，这里只是演示方法调用方式")
    print("实际使用时需要确保：")
    print("1. 网络连接正常")
    print("2. 已正确配置 access_token")
    print("3. API 服务器可访问")
    print()
    print("调用方式：")
    print("status, data, err_msg = FSManager.get_tester('RESSN10')")
    print()
    print("返回值说明：")
    print("- status: bool, 请求是否成功")
    print("- data: dict, 成功时包含测试人员信息")
    print("- err_msg: str, 失败时包含错误描述")
    
    print("=" * 50)


if __name__ == '__main__':
    print("选择测试模式:")
    print("1. 单元测试 (使用 Mock)")
    print("2. 手动测试演示")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        # 运行单元测试
        unittest.main(verbosity=2)
    elif choice == "2":
        # 运行手动测试演示
        run_manual_test()
    else:
        print("无效选择，运行单元测试")
        unittest.main(verbosity=2)
