# -*-coding:utf-8 -*-
"""
# Author     : jid
# Time       : 2025/4/3 11:28
# Description: 
"""
import datetime
import os.path
import threading

import xlwt

from common.LogUtils import logger
from power.tools.etm_mu3_control import etm_mu3_control
from utils.Influxdb import influx_client
from utils.SignalsManager import signals_manager


class WorkCurrentMonitorManager:

    def __init__(self):
        super().__init__()
        self.read_work_current_timer = None
        self.read_times = 0
        self.read_interval = 0
        self.max_read_times = 0
        self.channels = []
        self.timestamps = []
        self.channel1_work_current = []
        self.channel2_work_current = []
        self.channel3_work_current = []
        self.case_number = ""
        self.command = ""

    def handle_monitor_multi_channel_work_current(self, case_number, command, data):
        logger.info(f"handle_monitor_multi_channel_work_current case_number={case_number}, command={command}, "
                    f"data={data}")
        self.reset_params()
        self.case_number = case_number
        self.command = command
        channels_temp = data.split(",")[0]
        channels = channels_temp.split("|")
        self.read_interval = float(data.split(",")[1]) / 1000
        self.max_read_times = int(data.split(",")[2])
        volt_temp = data.split(",")[3]
        volts = volt_temp.split("|")
        self.channels = channels
        logger.info(f"handle_monitor_multi_channel_work_current channels={self.channels}, "
                    f"read_interval={self.read_interval}, max_read_times={self.max_read_times}")
        for i in range(len(channels)):
            etm_mu3_control.set_voltage(channel=int(self.channels[i]), value=float(volts[i]))

        self.read_work_current()

    def interrupt_stop(self):
        logger.info(f"interrupt_stop")
        self.stop_read_work_current()
        self.generate_excel_file()
        self.reset_params()

    def reset_params(self):
        self.read_times = 0
        self.read_interval = 0
        self.max_read_times = 0
        self.channels = []
        self.timestamps = []
        self.channel1_work_current = []
        self.channel2_work_current = []
        self.channel3_work_current = []
        self.case_number = ""
        self.command = ""

    def read_work_current(self):
        logger.info(f"read_work_current read_times={self.read_times}")
        if self.read_times >= self.max_read_times:
            signals_manager.step_execute_finish.emit(self.case_number, self.command, "PASS", "电流监控结束")
            self.stop_read_work_current()
            self.generate_excel_file()
            self.reset_params()
        else:
            self.read_work_current_timer = threading.Timer(interval=self.read_interval, function=self.read_work_current)
            self.read_work_current_timer.start()
            self.read_times += 1

        for i in range(len(self.channels)):
            status, work_current = etm_mu3_control.read_work_current(channel=int(self.channels[i]))
            logger.info(f"read_work_current channel={int(self.channels[i])}, status={status}, "
                        f"work_current={work_current}")
            if int(self.channels[i]) == 1:
                self.channel1_work_current.append(str(work_current))
            if int(self.channels[i]) == 2:
                self.channel2_work_current.append(str(work_current))
            if int(self.channels[i]) == 3:
                self.channel3_work_current.append(str(work_current))

        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
        self.timestamps.append(timestamp)
        logger.debug(f"read_work_current timestamps={self.timestamps}, "
                     f"channel1_work_current={self.channel1_work_current}, "
                     f"channel2_work_current={self.channel2_work_current}, "
                     f"channel3_work_current={self.channel3_work_current}")
        msg = f"已测试{self.read_times}次，剩余{self.max_read_times - self.read_times}次"
        signals_manager.step_execute_process.emit(self.case_number, self.command, msg)

    def stop_read_work_current(self):
        logger.info(f"stop_read_work_current")
        if self.read_work_current_timer is not None:
            self.read_work_current_timer.cancel()

    def generate_excel_file(self):
        logger.info(f"generate_excel_file")
        wb = xlwt.Workbook()
        ws = wb.add_sheet("Sheet1")
        ws.write(0, 0, "监控时间")
        ws.write(0, 1, "通道1工作电流")
        ws.write(0, 2, "通道2工作电流")
        ws.write(0, 3, "通道3工作电流")
        ws.col(0).width = 20 * 512
        ws.col(1).width = 20 * 256
        ws.col(2).width = 20 * 256
        ws.col(3).width = 20 * 256
        logger.info(f"generate_excel_file timestamps={self.timestamps}, "
                    f"channel1_work_current={self.channel1_work_current}, "
                    f"channel2_work_current={self.channel2_work_current}, "
                    f"channel3_work_current={self.channel3_work_current}")
        for i in range(len(self.channel3_work_current)):
            channel1_work_current = round(float(self.channel1_work_current[i]), 3)
            channel2_work_current = round(float(self.channel2_work_current[i]), 3)
            channel3_work_current = round(float(self.channel3_work_current[i]), 3)
            ws.write(i + 1, 0, self.timestamps[i])
            ws.write(i + 1, 1, f"{channel1_work_current}A")
            ws.write(i + 1, 2, f"{channel2_work_current}A")
            ws.write(i + 1, 3, f"{channel3_work_current}A")

        if not os.path.exists("work_current_monitor"):
            os.mkdir("work_current_monitor")

        timestamp = datetime.datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
        wb.save(os.path.join(os.getcwd(), "work_current_monitor", f"工作电流监控数据_{timestamp}.xls"))

    def read_all_work_current(self):
        items = []
        if not etm_mu3_control.is_open():
            return items
        for i in range(1, 4):
            try:
                status, work_current = etm_mu3_control.read_work_current(channel=i)
                if status:
                    items.append(work_current)
            except Exception as e:
                items = []
                logger.warning(f"read_all_work_current error: {e}")
                break
        return items

    def read_all_work_voltage(self):
        items = []
        if not etm_mu3_control.is_open():
            return items

        try:
            volt1 = etm_mu3_control.power_client.get_volt_p_ch1()
            volt2 = etm_mu3_control.power_client.get_volt_p_ch2()
            volt3 = etm_mu3_control.power_client.get_volt_p_ch3()

            items.append(volt1)
            items.append(volt2)
            items.append(volt3)
        except Exception as e:
            items = []
            logger.warning(f"read_all_work_current error: {e}")

        return items

    def update_work_current2influxdb(self, step):
        # 获取所有通道的工作电流

        work_current_values = self.read_all_work_current()
        work_voltage_values = self.read_all_work_voltage()
        try:
            from utils.ProjectManager import project_manager
            case_number = step.get("case_number", "")
            case_name = step.get("case_name", "")
            project_number = project_manager.get_test_plan_project_number()
            project_name = project_manager.get_test_plan_project_name()
            test_plan_name = project_manager.get_test_plan_name()
            test_plan_id = project_manager.get_test_plan_id()
            machine_number = project_manager.get_machine_number()
        except:
            return

        # 更新界面显示
        for i, value in enumerate(work_current_values):
            try:
                influx_client.write_data_multi(
                    table=str(project_number),
                    tags={
                        "project_name": project_name,
                        "test_plan_name": test_plan_name,
                        "test_plan_id": test_plan_id,
                        "machine_number": machine_number,
                        "case_number": case_number,
                        "case_name": case_name,
                        "channel": f"chl{i + 1}"
                    },
                    fields={"work_currency": float(value)}
                )
            except Exception as e:
                logger.warning(f"update_work_current2influxdb error: {e}")  # 记录错误日志，以便调试和排除问题
                break
        for i, value in enumerate(work_voltage_values):
            try:
                influx_client.write_data_multi(
                    table=str(project_number),
                    tags={
                        "project_name": project_name,
                        "test_plan_name": test_plan_name,
                        "test_plan_id": test_plan_id,
                        "machine_number": machine_number,
                        "case_number": case_number,
                        "case_name": case_name,
                        "channel": f"chl{i + 1}"
                    },
                    fields={"work_voltage": float(value)}
                )
            except Exception as e:
                logger.warning(f"update_work_current2influxdb error: {e}")  # 记录错误日志，以便调试和排除问题
                break


work_current_monitor_manager: WorkCurrentMonitorManager = WorkCurrentMonitorManager()
