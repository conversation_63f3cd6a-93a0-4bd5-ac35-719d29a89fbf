import os
import signal
import subprocess

from common.LogUtils import logger
from utils import get_process_pid


class CompressManager:
    def __init__(self, ffmpeg_path=None):
        if ffmpeg_path is None:
            # 方案A: 使用相对于当前脚本的路径尝试从系统PATH环境变量中寻找 ffmpeg
            base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            default_path = os.path.join(base_dir, "ffmpeg", "bin", "ffmpeg.exe")
            print(default_path)
            
            if os.path.exists(default_path):
                self.ffmpeg_executable = default_path
            else:
                # 方案B: 尝试从系统PATH环境变量中寻找 ffmpeg
                import shutil
                ffmpeg_executable = shutil.which("ffmpeg")
                if ffmpeg_executable:
                    self.ffmpeg_executable = ffmpeg_executable
                else:
                    # 如果所有方法都失败，则抛出异常
                    raise FileNotFoundError(
                        "无法自动找到 'ffmpeg'。请将其添加到系统PATH中，"
                        "或通过 'ffmpeg_path' 参数指定其完整路径。"
                    )
        else:
            # 如果用户提供了路径，则直接使用
            self.ffmpeg_executable = ffmpeg_path
            
        # 确认最终路径有效
        if not os.path.exists(self.ffmpeg_executable):
            raise FileNotFoundError(f"指定的 ffmpeg 路径无效或文件不存在: {self.ffmpeg_executable}")


    def compress_video(self, input_file, crf=23):
        """
        压缩视频文件
        """
        if not input_file or not os.path.exists(input_file):
            logger.error(f"输入文件不存在或为空: {input_file}")
            return None

        file_name, file_extension = os.path.splitext(input_file)
        output_file = f"{file_name}_compress.mp4"

        logger.info(f"开始压缩视频: {input_file}")

        command = [
            self.ffmpeg_executable,
            '-y',                        # 覆盖输出文件
            '-i', input_file,            # 输入文件
            '-c:v', 'libx264',           # 视频编码器
            '-crf', str(crf),            # 质量控制
            '-preset', 'medium',         # 编码速度预设
            '-c:a', 'libvo_aacenc',     # 音频编码器
            '-b:a', '128k',             # 音频比特率
            '-movflags', 'faststart',    # 快速启动优化
            '-pix_fmt', 'yuv420p',      # 像素格式兼容性
            '-map', '0:v:0',            # 映射第一个视频流
            '-map', '0:a:0?',           # 映射第一个音频流（如果存在）
            '-avoid_negative_ts', 'make_zero',  # 避免负时间戳
            output_file
        ]

        process = None
        try:
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            stdout, stderr = process.communicate(timeout=600)
            
            if process.returncode != 0:
                logger.error(f"FFmpeg执行失败，返回码: {process.returncode}")
                if stderr:
                    logger.error(f"错误信息: {stderr}")
                return None
            
            # 记录音频处理信息（仅用于调试）
            # if stderr:
            #     if 'no audio' in stderr.lower() or 'does not contain any stream' in stderr:
            #         logger.info("输入文件无音频轨道，仅处理视频")
            #     elif 'audio' in stderr.lower():
            #         logger.debug("音频处理信息已记录")
            
            # 验证输出文件
            if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
                original_size = os.path.getsize(input_file)
                compressed_size = os.path.getsize(output_file)
                compression_ratio = (1 - compressed_size / original_size) * 100
                
                logger.info(f"压缩完成: {output_file}")
                # logger.info(f"原始大小: {original_size:,} bytes")
                # logger.info(f"压缩后大小: {compressed_size:,} bytes")
                logger.info(f"压缩率: {compression_ratio:.1f}%")
                
                return output_file
            else:
                logger.error(f"输出文件无效: {output_file}")
                return None

        except subprocess.TimeoutExpired:
            logger.error("视频压缩超时")
            if process:
                process.kill()
            return None
        except FileNotFoundError:
            logger.error(f"FFmpeg程序未找到: {self.ffmpeg_executable}")
            return None
        except Exception as e:
            logger.error(f"压缩过程中发生错误: {str(e)}")
            return None
        finally:
            if process and process.poll() is None:
                process.terminate()
                process.wait()



    # def compress_video(self, input_video_path, crf=1):
    #     logger.info(f"compress_video input_video_path={input_video_path}, crf={crf}")
    #     # 记录开始时间
    #     start_time = time.time()
    #     output_video_path = input_video_path.replace('.mp4', '_compress.mp4')
    #     ffmpeg_pah = os.path.join(os.getcwd(), "ffmpeg", "bin", "ffmpeg.exe")
    #     ffmpeg_cmd = [
    #         ffmpeg_pah,
    #         '-i', input_video_path,
    #         '-c:v', 'libxvid',  # 使用 libxvid 编码器来处理 MP4V 格式
    #         '-q:v', str(crf),  # 设置视频质量
    #         '-c:a', 'aac',
    #         '-strict', 'experimental',
    #         '-b:a', '1000k',
    #         output_video_path
    #     ]

    #     try:
    #         subprocess.run(ffmpeg_cmd, check=True)
    #         os.remove(input_video_path)
    #         logger.info('compress_video 原视频删除完成')
    #         # 记录结束时间
    #         end_time = time.time()
    #         # 计算耗时
    #         elapsed_time = (end_time - start_time)
    #         logger.info(f"compress_video output_video_path={output_video_path}视频压缩完成，耗时：{elapsed_time}秒")
    #     except Exception as e:
    #         logger.error(f"compress_video 视频压缩失败: {str(e.args)}")

    #     self.kill_process_pid()

    @staticmethod
    def kill_process_pid():
        pid = get_process_pid("ffmpeg.exe")
        logger.info(f"kill_process_pid pid={pid}")
        if pid is not None:
            os.kill(pid, signal.SIGTERM)
            logger.info(f"kill_process_pid kill success")


compress_manager: CompressManager = CompressManager()
