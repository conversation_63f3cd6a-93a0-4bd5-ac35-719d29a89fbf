# -*- mode: python ; coding: utf-8 -*-

import sys
block_cipher = None

# 定义所有需要动态导入的模块
hidden_import_modules = [
    'canmatrix.formats.dbc',
    'command_handlers.vds_commands',
    'command_handlers.touch_motion_commands', 
    'command_handlers.power_commands',
    'command_handlers.vision_commands',
    'command_handlers.timing_commands',
    'command_handlers.bat_script_commands',
    'command_handlers.adb_commands',
    'command_handlers.fault_simulation_commands',
    'command_handlers.color_temp_commands',
    'command_handlers.brightness_commands',
    'command_handlers.log_commands',
    'command_handlers.service_27_commands',
    'command_handlers.can_commands',
    'command_handlers.lin_commands',
    'command_handlers.mate_motion_commands',
    'command_handlers.relay_commands',
    'command_handlers.oscilloscope_commands',
    'command_handlers.light_sensor_commands',
    'command_handlers.temperature_commands',
    'command_handlers.color_analyzer_commands',
    'command_handlers.endurance_test_commands',
    'command_handlers.dtc_commands',
    'command_handlers.imu_sensor_commands',
]


a = Analysis(
    ['ATEApp.py'],
    pathex=[""],
    binaries=[],
    datas=[
    ('images', 'images'),
    ('res', 'res'),
    ('control_board', 'control_board'),
    ('checkpoints', 'checkpoints'),
    ('configs', 'configs'),
    ("qt_material", "qt_material"),
    ('orbbec', 'orbbec'),
    ('algorithm_server', 'algorithm_server'),
    ('adb/zlgcan', 'adb/zlgcan'),
    ('adb/pcan', 'adb/pcan'),
    ('adb/lin', 'adb/lin'),
    ('ffmpeg', 'ffmpeg'),
    ('external_program/KingstVISQTTool','external_program/KingstVISQTTool'),
    ('tools/endurance/configs', 'tools/endurance/configs'),
    ('external_program/KingstVISQTTool/KingstVIS/', 'external_program/KingstVISQTTool/KingstVIS/'),
    ('power/configs', 'power/configs'),
    ('tools/can_tool/devices/canmatrix', 'canmatrix'),
    ('tools/can_tool/devices/future', 'future'),
    ('tools/can_tool/devices/past', 'past'),
    ('adb/TSMaster/libTSCANAPI/', 'libTSCANAPI'),
    ('adb/dll', 'adb/dll'),
    ('photics/dll', 'photics/dll'),
    ],
    hiddenimports=hidden_import_modules,
    hookspath=[],
    runtime_hooks=[],
    excludes=['importlib_resources',],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,

)
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='ATEApp',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=True,
    icon='.\\res\\HWTC.ico'
)
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=['api-ms-win-crt-multibyte-l1-1-0.dll'],
    name='ATEApp',
)
