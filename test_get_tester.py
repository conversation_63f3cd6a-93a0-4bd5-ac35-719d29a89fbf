# -*- coding: utf-8 -*-
"""
<AUTHOR> Assistant
@Date   : 2025-01-23
@Desc   : 测试 FSManager.get_tester 方法
"""

import sys
import os
import unittest
from unittest.mock import patch, Mock

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import requests
except ImportError:
    print("警告: requests 模块未安装，某些测试可能无法正常运行")
    requests = None

from fs_manager.FSManager import FSManager


class TestGetTester(unittest.TestCase):
    """测试 FSManager.get_tester 方法"""

    def setUp(self):
        """测试前的准备工作"""
        self.test_project_number = "RESSN10"
        self.fs_manager = FSManager()

    @patch('fs_manager.FSManager.project_manager')
    @patch('fs_manager.FSManager.requests.get')
    def test_get_tester_success(self, mock_get, mock_project_manager):
        """测试成功获取测试人员信息"""
        # 模拟 project_manager.get_access_token() 返回值
        mock_project_manager.get_access_token.return_value = "test_token"
        
        # 模拟成功的 API 响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "err_code": 0,
            "msg": "success",
            "data": {
                "testers": [
                    {"id": 1, "name": "张三", "email": "<EMAIL>"},
                    {"id": 2, "name": "李四", "email": "<EMAIL>"}
                ]
            }
        }
        mock_get.return_value = mock_response
        
        # 调用方法
        status, data, err_msg = FSManager.get_tester(self.test_project_number)
        
        # 验证结果
        self.assertTrue(status)
        self.assertIsNotNone(data)
        self.assertEqual(err_msg, "")
        self.assertIn("testers", data)
        self.assertEqual(len(data["testers"]), 2)
        
        # 验证调用参数
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        self.assertIn("project_number", call_args.kwargs["params"])
        self.assertEqual(call_args.kwargs["params"]["project_number"], self.test_project_number)

    @patch('fs_manager.FSManager.project_manager')
    @patch('fs_manager.FSManager.requests.get')
    def test_get_tester_api_error(self, mock_get, mock_project_manager):
        """测试 API 返回错误"""
        # 模拟 project_manager.get_access_token() 返回值
        mock_project_manager.get_access_token.return_value = "test_token"
        
        # 模拟 API 错误响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "err_code": 1001,
            "msg": "项目不存在",
            "data": None
        }
        mock_get.return_value = mock_response
        
        # 调用方法
        status, data, err_msg = FSManager.get_tester(self.test_project_number)
        
        # 验证结果
        self.assertFalse(status)
        self.assertIsNone(data)
        self.assertIn("项目不存在", err_msg)

    @patch('fs_manager.FSManager.project_manager')
    @patch('fs_manager.FSManager.requests.get')
    def test_get_tester_400_error(self, mock_get, mock_project_manager):
        """测试 400 错误（请求参数错误）"""
        # 模拟 project_manager.get_access_token() 返回值
        mock_project_manager.get_access_token.return_value = "test_token"

        # 模拟 400 错误响应
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.json.return_value = {
            "msg": "项目编号格式错误"
        }
        mock_get.return_value = mock_response

        # 调用方法 - 使用有效的项目编号，让它通过参数验证
        status, data, err_msg = FSManager.get_tester("INVALID_PROJECT")

        # 验证结果
        self.assertFalse(status)
        self.assertIsNone(data)
        self.assertIn("请求参数错误", err_msg)
        self.assertIn("项目编号格式错误", err_msg)

    @patch('fs_manager.FSManager.project_manager')
    @patch('fs_manager.FSManager.requests.get')
    def test_get_tester_401_error(self, mock_get, mock_project_manager):
        """测试 401 错误（认证失败）"""
        # 模拟 project_manager.get_access_token() 返回值
        mock_project_manager.get_access_token.return_value = "invalid_token"
        
        # 模拟 401 错误响应
        mock_response = Mock()
        mock_response.status_code = 401
        mock_response.json.return_value = {
            "code": "UNAUTHORIZED",
            "detail": "Token 无效"
        }
        mock_get.return_value = mock_response
        
        # 调用方法
        status, data, err_msg = FSManager.get_tester(self.test_project_number)
        
        # 验证结果
        self.assertFalse(status)
        self.assertIsNone(data)
        self.assertIn("认证失败", err_msg)

    @patch('fs_manager.FSManager.project_manager')
    @patch('fs_manager.FSManager.requests.get')
    def test_get_tester_timeout(self, mock_get, mock_project_manager):
        """测试请求超时"""
        # 模拟 project_manager.get_access_token() 返回值
        mock_project_manager.get_access_token.return_value = "test_token"
        
        # 模拟超时异常
        if requests:
            mock_get.side_effect = requests.exceptions.Timeout()
        else:
            mock_get.side_effect = Exception("Timeout")
        
        # 调用方法
        status, data, err_msg = FSManager.get_tester(self.test_project_number)
        
        # 验证结果
        self.assertFalse(status)
        self.assertIsNone(data)
        self.assertIn("请求超时", err_msg)

    @patch('fs_manager.FSManager.project_manager')
    @patch('fs_manager.FSManager.requests.get')
    def test_get_tester_connection_error(self, mock_get, mock_project_manager):
        """测试网络连接错误"""
        # 模拟 project_manager.get_access_token() 返回值
        mock_project_manager.get_access_token.return_value = "test_token"
        
        # 模拟连接错误
        if requests:
            mock_get.side_effect = requests.exceptions.ConnectionError()
        else:
            mock_get.side_effect = Exception("Connection Error")
        
        # 调用方法
        status, data, err_msg = FSManager.get_tester(self.test_project_number)
        
        # 验证结果
        self.assertFalse(status)
        self.assertIsNone(data)
        self.assertIn("网络连接失败", err_msg)

    def test_get_tester_with_different_project_numbers(self):
        """测试不同的项目编号格式"""
        # 只测试有效的项目编号格式，空字符串会被参数验证拦截
        valid_test_cases = [
            "RESSN10",
            "ABC123",
            "TEST_PROJECT_001",
            "项目编号中文"
        ]

        with patch('fs_manager.FSManager.project_manager') as mock_project_manager, \
             patch('fs_manager.FSManager.requests.get') as mock_get:

            mock_project_manager.get_access_token.return_value = "test_token"
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "err_code": 0,
                "msg": "success",
                "data": []
            }
            mock_get.return_value = mock_response

            for project_number in valid_test_cases:
                with self.subTest(project_number=project_number):
                    status, data, err_msg = FSManager.get_tester(project_number)

                    # 验证调用参数 - 项目编号会被 strip() 处理
                    call_args = mock_get.call_args
                    self.assertEqual(
                        call_args.kwargs["params"]["project_number"],
                        project_number.strip()
                    )

    @patch('fs_manager.FSManager.project_manager')
    @patch('fs_manager.FSManager.requests.get')
    def test_get_tester_with_fixed_bearer_token(self, mock_get, mock_project_manager):
        """测试使用固定 Bearer Token"""
        # 设置固定的 Bearer Token
        fixed_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUzNzc3OTQwLCJpYXQiOjE3NTM2OTE1NDAsImp0aSI6IjczMjA4Mzk4ZGE4MTQxNDc4MGU4NWYzY2YyMGVhYjIzIiwidXNlcl9pZCI6MTgxfQ.SHUv-BmzTOfSlX-GqFVckOscEjTZJIQpUv6HnZ1HwoI"
        mock_project_manager.get_access_token.return_value = fixed_token

        # 模拟成功的 API 响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "err_code": 0,
            "msg": "success",
            "data": {
                "testers": [
                    {"id": 1, "name": "张三", "email": "<EMAIL>"},
                    {"id": 2, "name": "李四", "email": "<EMAIL>"}
                ]
            }
        }
        mock_get.return_value = mock_response

        # 调用方法
        status, data, err_msg = FSManager.get_tester(self.test_project_number)

        # 验证结果
        self.assertTrue(status)
        self.assertIsNotNone(data)
        self.assertEqual(err_msg, "")
        if data:
            self.assertIn("testers", data)

        # 验证请求头中包含正确的 Bearer Token
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        headers = call_args.kwargs.get("headers", {})
        self.assertIn("Authorization", headers)
        self.assertEqual(headers["Authorization"], f"Bearer {fixed_token}")

        # 验证调用参数
        self.assertIn("project_number", call_args.kwargs["params"])
        self.assertEqual(call_args.kwargs["params"]["project_number"], self.test_project_number)

    @patch('fs_manager.FSManager.project_manager')
    @patch('fs_manager.FSManager.requests.get')
    def test_get_tester_project_not_configured_error(self, mock_get, mock_project_manager):
        """测试项目相关人未配置错误 (err_code=1)"""
        # 模拟 project_manager.get_access_token() 返回值
        mock_project_manager.get_access_token.return_value = "test_token"

        # 模拟项目相关人未配置的 API 响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "err_code": 1,
            "msg": "项目相关人未配置"
        }
        mock_get.return_value = mock_response

        # 调用方法
        status, data, err_msg = FSManager.get_tester(self.test_project_number)

        # 验证结果
        self.assertFalse(status)
        self.assertIsNone(data)
        self.assertIn("项目相关人未配置", err_msg)
        self.assertIn("请联系管理员配置", err_msg)

    @patch('fs_manager.FSManager.project_manager')
    @patch('fs_manager.FSManager.requests.get')
    def test_get_tester_empty_project_number(self, mock_get, mock_project_manager):
        """测试空项目编号"""
        # 模拟 project_manager.get_access_token() 返回值
        mock_project_manager.get_access_token.return_value = "test_token"

        # 测试空字符串
        status, data, err_msg = FSManager.get_tester("")
        self.assertFalse(status)
        self.assertIsNone(data)
        self.assertIn("项目编号不能为空", err_msg)

        # 测试只有空格的字符串
        status, data, err_msg = FSManager.get_tester("   ")
        self.assertFalse(status)
        self.assertIsNone(data)
        self.assertIn("项目编号不能为空", err_msg)

        # 测试 None
        status, data, err_msg = FSManager.get_tester(None)
        self.assertFalse(status)
        self.assertIsNone(data)
        self.assertIn("项目编号不能为空", err_msg)

        # 验证没有发起网络请求
        mock_get.assert_not_called()

    @patch('fs_manager.FSManager.project_manager')
    @patch('fs_manager.FSManager.requests.get')
    def test_get_tester_empty_access_token(self, mock_get, mock_project_manager):
        """测试访问令牌为空"""
        # 模拟 project_manager.get_access_token() 返回空值
        mock_project_manager.get_access_token.return_value = ""

        # 调用方法
        status, data, err_msg = FSManager.get_tester(self.test_project_number)

        # 验证结果
        self.assertFalse(status)
        self.assertIsNone(data)
        self.assertIn("访问令牌未设置", err_msg)
        self.assertIn("请先登录", err_msg)

        # 验证没有发起网络请求
        mock_get.assert_not_called()

    @patch('fs_manager.FSManager.project_manager')
    @patch('fs_manager.FSManager.requests.get')
    def test_get_tester_403_error(self, mock_get, mock_project_manager):
        """测试 403 权限不足错误"""
        # 模拟 project_manager.get_access_token() 返回值
        mock_project_manager.get_access_token.return_value = "test_token"

        # 模拟 403 错误响应
        mock_response = Mock()
        mock_response.status_code = 403
        mock_get.return_value = mock_response

        # 调用方法
        status, data, err_msg = FSManager.get_tester(self.test_project_number)

        # 验证结果
        self.assertFalse(status)
        self.assertIsNone(data)
        self.assertIn("权限不足", err_msg)

    @patch('fs_manager.FSManager.project_manager')
    @patch('fs_manager.FSManager.requests.get')
    def test_get_tester_500_error(self, mock_get, mock_project_manager):
        """测试 500 服务器内部错误"""
        # 模拟 project_manager.get_access_token() 返回值
        mock_project_manager.get_access_token.return_value = "test_token"

        # 模拟 500 错误响应
        mock_response = Mock()
        mock_response.status_code = 500
        mock_get.return_value = mock_response

        # 调用方法
        status, data, err_msg = FSManager.get_tester(self.test_project_number)

        # 验证结果
        self.assertFalse(status)
        self.assertIsNone(data)
        self.assertIn("服务器内部错误", err_msg)


def run_manual_test():
    """手动测试函数，用于实际调用 API"""
    print("=" * 50)
    print("手动测试 FSManager.get_tester 方法")
    print("=" * 50)

    # 测试项目编号
    test_project_number = "RESSN10"

    print(f"测试项目编号: {test_project_number}")
    print("-" * 30)

    try:
        # 设置固定的 Bearer Token
        fixed_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUzNzc3OTQwLCJpYXQiOjE3NTM2OTE1NDAsImp0aSI6IjczMjA4Mzk4ZGE4MTQxNDc4MGU4NWYzY2YyMGVhYjIzIiwidXNlcl9pZCI6MTgxfQ.SHUv-BmzTOfSlX-GqFVckOscEjTZJIQpUv6HnZ1HwoI"

        # 临时设置 token
        from fs_manager.FSManager import project_manager
        original_token = project_manager.get_access_token()
        project_manager.set_access_token(fixed_token)

        print(f"使用固定 Bearer Token: {fixed_token[:50]}...")
        print("-" * 30)

        # 调用方法
        status, data, err_msg = FSManager.get_tester(test_project_number)

        print(f"请求状态: {'成功' if status else '失败'}")
        print(f"错误信息: {err_msg}")

        if status and data:
            print("返回数据:")
            import json
            print(json.dumps(data, indent=2, ensure_ascii=False))
        else:
            print("无返回数据")

        # 恢复原始 token
        project_manager.set_access_token(original_token)

    except Exception as e:
        print(f"测试异常: {str(e)}")
        import traceback
        traceback.print_exc()

    print("=" * 50)


def run_fixed_token_test():
    """使用固定 Bearer Token 的手动测试函数"""
    print("=" * 50)
    print("使用固定 Bearer Token 测试 FSManager.get_tester 方法")
    print("=" * 50)

    # 测试项目编号
    test_project_number = "WPTSN11"

    # 固定的 Bearer Token
    fixed_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUzNzc3OTQwLCJpYXQiOjE3NTM2OTE1NDAsImp0aSI6IjczMjA4Mzk4ZGE4MTQxNDc4MGU4NWYzY2YyMGVhYjIzIiwidXNlcl9pZCI6MTgxfQ.SHUv-BmzTOfSlX-GqFVckOscEjTZJIQpUv6HnZ1HwoI"

    print(f"测试项目编号: {test_project_number}")
    print(f"使用固定 Bearer Token: {fixed_token[:50]}...")
    print("-" * 30)

    try:
        # 导入并设置 token
        from fs_manager.FSManager import project_manager

        # 保存原始 token
        original_token = project_manager.get_access_token()

        # 设置固定 token
        project_manager.set_access_token(fixed_token)

        print("已设置固定 Bearer Token")
        print("-" * 30)

        # 调用方法
        status, data, err_msg = FSManager.get_tester(test_project_number)

        print(f"请求状态: {'成功' if status else '失败'}")
        print(f"错误信息: {err_msg}")

        if status and data:
            print("返回数据:")
            import json
            print(json.dumps(data, indent=2, ensure_ascii=False))
        else:
            print("无返回数据")

        # 恢复原始 token
        project_manager.set_access_token(original_token)
        print("-" * 30)
        print("已恢复原始 token")

    except Exception as e:
        print(f"测试异常: {str(e)}")
        import traceback
        traceback.print_exc()

    print("=" * 50)


if __name__ == '__main__':
    print("选择测试模式:")
    print("1. 单元测试 (使用 Mock)")
    print("2. 手动测试 (实际调用 API)")
    print("3. 固定 Bearer Token 测试 (实际调用 API)")

    choice = input("请输入选择 (1, 2 或 3): ").strip()

    if choice == "1":
        # 运行单元测试
        unittest.main(verbosity=2)
    elif choice == "2":
        # 运行手动测试
        run_manual_test()
    elif choice == "3":
        # 运行固定 Bearer Token 测试
        run_fixed_token_test()
    else:
        print("无效选择，运行单元测试")
        unittest.main(verbosity=2)
