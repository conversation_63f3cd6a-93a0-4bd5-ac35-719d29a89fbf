# -*- coding: utf-8 -*-
"""
<AUTHOR> Assistant
@Date   : 2025-01-23
@Desc   : 测试 FSManager.get_tester 方法
"""

import sys
import os
import unittest
from unittest.mock import patch, Mock

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import requests
except ImportError:
    print("警告: requests 模块未安装，某些测试可能无法正常运行")
    requests = None

from fs_manager.FSManager import FSManager


class TestGetTester(unittest.TestCase):
    """测试 FSManager.get_tester 方法"""

    def setUp(self):
        """测试前的准备工作"""
        self.test_project_number = "RESSN10"
        self.fs_manager = FSManager()

    @patch('fs_manager.FSManager.project_manager')
    @patch('fs_manager.FSManager.requests.get')
    def test_get_tester_success(self, mock_get, mock_project_manager):
        """测试成功获取测试人员信息"""
        # 模拟 project_manager.get_access_token() 返回值
        mock_project_manager.get_access_token.return_value = "test_token"
        
        # 模拟成功的 API 响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "err_code": 0,
            "msg": "success",
            "data": {
                "testers": [
                    {"id": 1, "name": "张三", "email": "<EMAIL>"},
                    {"id": 2, "name": "李四", "email": "<EMAIL>"}
                ]
            }
        }
        mock_get.return_value = mock_response
        
        # 调用方法
        status, data, err_msg = FSManager.get_tester(self.test_project_number)
        
        # 验证结果
        self.assertTrue(status)
        self.assertIsNotNone(data)
        self.assertEqual(err_msg, "")
        self.assertIn("testers", data)
        self.assertEqual(len(data["testers"]), 2)
        
        # 验证调用参数
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        self.assertIn("project_number", call_args.kwargs["params"])
        self.assertEqual(call_args.kwargs["params"]["project_number"], self.test_project_number)

    @patch('fs_manager.FSManager.project_manager')
    @patch('fs_manager.FSManager.requests.get')
    def test_get_tester_api_error(self, mock_get, mock_project_manager):
        """测试 API 返回错误"""
        # 模拟 project_manager.get_access_token() 返回值
        mock_project_manager.get_access_token.return_value = "test_token"
        
        # 模拟 API 错误响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "err_code": 1001,
            "msg": "项目不存在",
            "data": None
        }
        mock_get.return_value = mock_response
        
        # 调用方法
        status, data, err_msg = FSManager.get_tester(self.test_project_number)
        
        # 验证结果
        self.assertFalse(status)
        self.assertIsNone(data)
        self.assertIn("项目不存在", err_msg)

    @patch('fs_manager.FSManager.project_manager')
    @patch('fs_manager.FSManager.requests.get')
    def test_get_tester_400_error(self, mock_get, mock_project_manager):
        """测试 400 错误（请求参数错误）"""
        # 模拟 project_manager.get_access_token() 返回值
        mock_project_manager.get_access_token.return_value = "test_token"
        
        # 模拟 400 错误响应
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.json.return_value = {
            "msg": "项目编号不能为空"
        }
        mock_get.return_value = mock_response
        
        # 调用方法
        status, data, err_msg = FSManager.get_tester("")
        
        # 验证结果
        self.assertFalse(status)
        self.assertIsNone(data)
        self.assertIn("请求参数错误", err_msg)
        self.assertIn("项目编号不能为空", err_msg)

    @patch('fs_manager.FSManager.project_manager')
    @patch('fs_manager.FSManager.requests.get')
    def test_get_tester_401_error(self, mock_get, mock_project_manager):
        """测试 401 错误（认证失败）"""
        # 模拟 project_manager.get_access_token() 返回值
        mock_project_manager.get_access_token.return_value = "invalid_token"
        
        # 模拟 401 错误响应
        mock_response = Mock()
        mock_response.status_code = 401
        mock_response.json.return_value = {
            "code": "UNAUTHORIZED",
            "detail": "Token 无效"
        }
        mock_get.return_value = mock_response
        
        # 调用方法
        status, data, err_msg = FSManager.get_tester(self.test_project_number)
        
        # 验证结果
        self.assertFalse(status)
        self.assertIsNone(data)
        self.assertIn("认证失败", err_msg)

    @patch('fs_manager.FSManager.project_manager')
    @patch('fs_manager.FSManager.requests.get')
    def test_get_tester_timeout(self, mock_get, mock_project_manager):
        """测试请求超时"""
        # 模拟 project_manager.get_access_token() 返回值
        mock_project_manager.get_access_token.return_value = "test_token"
        
        # 模拟超时异常
        if requests:
            mock_get.side_effect = requests.exceptions.Timeout()
        else:
            mock_get.side_effect = Exception("Timeout")
        
        # 调用方法
        status, data, err_msg = FSManager.get_tester(self.test_project_number)
        
        # 验证结果
        self.assertFalse(status)
        self.assertIsNone(data)
        self.assertIn("请求超时", err_msg)

    @patch('fs_manager.FSManager.project_manager')
    @patch('fs_manager.FSManager.requests.get')
    def test_get_tester_connection_error(self, mock_get, mock_project_manager):
        """测试网络连接错误"""
        # 模拟 project_manager.get_access_token() 返回值
        mock_project_manager.get_access_token.return_value = "test_token"
        
        # 模拟连接错误
        if requests:
            mock_get.side_effect = requests.exceptions.ConnectionError()
        else:
            mock_get.side_effect = Exception("Connection Error")
        
        # 调用方法
        status, data, err_msg = FSManager.get_tester(self.test_project_number)
        
        # 验证结果
        self.assertFalse(status)
        self.assertIsNone(data)
        self.assertIn("网络连接失败", err_msg)

    def test_get_tester_with_different_project_numbers(self):
        """测试不同的项目编号格式"""
        test_cases = [
            "RESSN10",
            "ABC123",
            "TEST_PROJECT_001",
            "项目编号中文",
            ""
        ]
        
        with patch('fs_manager.FSManager.project_manager') as mock_project_manager, \
             patch('fs_manager.FSManager.requests.get') as mock_get:
            
            mock_project_manager.get_access_token.return_value = "test_token"
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "err_code": 0,
                "msg": "success",
                "data": {"testers": []}
            }
            mock_get.return_value = mock_response
            
            for project_number in test_cases:
                with self.subTest(project_number=project_number):
                    status, data, err_msg = FSManager.get_tester(project_number)
                    
                    # 验证调用参数
                    call_args = mock_get.call_args
                    self.assertEqual(
                        call_args.kwargs["params"]["project_number"], 
                        project_number
                    )


def run_manual_test():
    """手动测试函数，用于实际调用 API"""
    print("=" * 50)
    print("手动测试 FSManager.get_tester 方法")
    print("=" * 50)
    
    # 测试项目编号
    test_project_number = "RESSN10"
    
    print(f"测试项目编号: {test_project_number}")
    print("-" * 30)
    
    try:
        # 调用方法
        status, data, err_msg = FSManager.get_tester(test_project_number)
        
        print(f"请求状态: {'成功' if status else '失败'}")
        print(f"错误信息: {err_msg}")
        
        if status and data:
            print("返回数据:")
            import json
            print(json.dumps(data, indent=2, ensure_ascii=False))
        else:
            print("无返回数据")
            
    except Exception as e:
        print(f"测试异常: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("=" * 50)


if __name__ == '__main__':
    print("选择测试模式:")
    print("1. 单元测试 (使用 Mock)")
    print("2. 手动测试 (实际调用 API)")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        # 运行单元测试
        unittest.main(verbosity=2)
    elif choice == "2":
        # 运行手动测试
        run_manual_test()
    else:
        print("无效选择，运行单元测试")
        unittest.main(verbosity=2)
