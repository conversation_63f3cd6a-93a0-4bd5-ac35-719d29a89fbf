# -*- coding: utf-8 -*-
"""
<AUTHOR> jid
@Date   : 2024/3/27 10:21
@Desc   : 
"""


class Url:
    base_url = "http://10.1.1.131:9000"
    # base_url = "http://10.1.8.138:9091"
    # base_url = "http://hq.hwauto.com.cn:10011"


fs_send_msg = "/auto_test/send_msg"
fs_send_exception_msg = "/auto_test/send_test_exception_msg"
user_login = "/users/login2"
report_issues = "/issues"
upload_tc_file = "/test_m/test_cases"
download_case = "/test_m/test_cases/tc_file"
get_case_functions = "/functions"
get_case_types = "/test_m/test_case_types"
get_case_status = "/test_m/test_case_status"
get_test_plans = "/test_plans"
get_v2_test_plans = "/v2/test_plans"
update_sub_plan_status_url = "/test_plans/update_sub_plan_status"
post_test_records = "/test_records"
post_v2_test_records = "/v2/test_records"
post_test_records_items_resources = "/test_records/items/resources"
post_v2_test_records_items_resources = "/v2/test_records/items/resources"
get_all_projects = "/projects/p/all"
get_machine_detail = "/machines/detail/by_number"
get_project_extra_info = "/projects/extra_info/by_number"
post_test_exception_msg = "/auto_test/send_test_exception_msg"
post_machine_storage_alarm_msg = "/auto_test/send_machine_storage_alarm_msg"
post_test_completed_msg = "/auto_test/send_test_completed_msg"
post_test_case_exec = "/v2/test_plans/test_case_exec"
post_process_monitor_send_exp_msg = "/process_monitor/send_exp_msg"
post_process_monitor_exp_submit = "/process_monitor/exp_submit"
get_tester = "/auto_test/get_tester"

