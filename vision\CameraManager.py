import queue
import threading
import time
import traceback
from enum import Enum

import cv2
from PyQt5.QtGui import QImage, QPixmap

from common import is_windows_platform
from common.LogUtils import logger
from utils.SignalsManager import signals_manager
from vision.CameraConfig import camera_config
from vision.VisualDetectSignal import visual_detect_signal


class CameraStatus(Enum):
    IDLE = 0
    SEARCHING = 1
    CONNECTING = 2
    CONNECTED = 3
    DISCONNECTED = 4


class CameraManager:

    def __init__(self):
        self.camera_dic = {}
        self.video_list_calibrate = queue.Queue()
        self.video_list_function = queue.Queue()
        self.video_list_monitor = queue.Queue()
        self.function_flag = False
        self.calibrate_flag = False
        self.preview_flag = False
        self.monitor_flag = False
        self.start_collect_flag = False
        self.start_collect = True
        self.cap = None
        self.current_camera_id = 0
        self.status = CameraStatus.IDLE

    def connect_video(self, camera_id):
        logger.info(f"connect_video camera_id={camera_id}")
        if not self.status == CameraStatus.CONNECTING:
            self.status = CameraStatus.CONNECTING
            signals_manager.vision_calibrate_enable_status.emit(False)
            signals_manager.update_device_process_signal.emit("相机", "连接中...")
        else:
            return logger.warning(f"connect_video 相机正在连接中")

        self.current_camera_id = camera_id
        self.cap = cv2.VideoCapture(camera_id, cv2.CAP_DSHOW)
        if is_windows_platform():
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 720)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 600)
            self.cap.set(cv2.CAP_PROP_FPS, 30)
        logger.info(f"connect_video set_exposure={camera_config.exposure}")
        # 设置曝光值(一般情况下，曝光值的范围在-10到10之间，具体数值需要根据相机和环境进行调整)
        self.cap.set(cv2.CAP_PROP_EXPOSURE, camera_config.exposure)
        exposure = self.cap.get(cv2.CAP_PROP_EXPOSURE)
        logger.info(f"connect_video get_exposure={exposure}")
        self.camera_dic[camera_id] = self.cap
        self.status = CameraStatus.CONNECTED
        self.preview_flag = True
        signals_manager.vision_calibrate_enable_status.emit(True)
        signals_manager.update_device_status_signal.emit("相机", True)

        if not self.start_collect_flag:
            self.start_collect_flag = True
            threading.Thread(target=self.update_frame, args=(camera_id,)).start()

    def update_cap_prop_exposure(self):
        logger.info(f"update_cap_prop_exposure set_exposure={camera_config.exposure}")
        signals_manager.vision_calibrate_enable_status.emit(False)
        if self.cap is not None:
            self.cap.set(cv2.CAP_PROP_EXPOSURE, camera_config.exposure)
            exposure = self.cap.get(cv2.CAP_PROP_EXPOSURE)
            logger.info(f"update_cap_prop_exposure get_exposure={exposure}")
        signals_manager.vision_calibrate_enable_status.emit(True)

    def is_open(self):
        return self.start_collect_flag

    def close_video(self, camera_id):
        logger.info(f"close_video camera_id={camera_id}")
        cap = self.camera_dic.get(camera_id)
        if cap is not None:
            cap.release()
        self.start_collect_flag = False
        self.status = CameraStatus.DISCONNECTED
        return True

    def update_frame(self, camera_id):
        logger.info(f"update_frame camera_id={camera_id}, start_collect_flag={self.start_collect_flag}")
        while self.start_collect_flag:
            time.sleep(0.05)
            if self.function_flag or self.calibrate_flag or self.preview_flag or self.monitor_flag:
                self.cap = self.camera_dic.get(camera_id)
                # 读取一帧视频
                try:
                    ret, frame = self.cap.read()
                    if ret:
                        if self.calibrate_flag:
                            self.video_list_calibrate.put(frame)

                        if self.function_flag:
                            self.video_list_function.put(frame)

                        if self.monitor_flag:
                            self.video_list_monitor.put(frame)

                        image = QImage(frame, frame.shape[1], frame.shape[0], QImage.Format_BGR888)
                        # 将 QImage 转换为 QPixmap
                        pixmap = QPixmap.fromImage(image)
                        visual_detect_signal.video_change.emit(pixmap)
                except Exception:
                    print(traceback.format_exc())

    def start_video(self, index):
        logger.info(f"start_video index={index}")
        if self.start_collect_flag:
            if index == 0:
                # 停止检测
                self.function_flag = False
            elif index == 1:
                # 功能检测
                self.function_flag = True
            elif index == 2:
                # 标定画面
                self.calibrate_flag = True
            elif index == 3:
                # 视频预览
                self.preview_flag = True


camera_manager: CameraManager = CameraManager()
