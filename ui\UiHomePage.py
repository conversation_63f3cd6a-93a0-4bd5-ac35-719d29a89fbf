# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'UiHomePage.ui'
#
# Created by: PyQt5 UI code generator 5.15.6
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        MainWindow.setObjectName("MainWindow")
        MainWindow.resize(1000, 600)
        MainWindow.setMinimumSize(QtCore.QSize(1000, 600))
        MainWindow.setStyleSheet("")
        self.centralwidget = QtWidgets.QWidget(MainWindow)
        self.centralwidget.setObjectName("centralwidget")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.centralwidget)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout()
        self.verticalLayout_3.setContentsMargins(0, 20, -1, -1)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setContentsMargins(0, 5, -1, -1)
        self.horizontalLayout.setSpacing(6)
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem = QtWidgets.QSpacerItem(25, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.verticalLayout_5 = QtWidgets.QVBoxLayout()
        self.verticalLayout_5.setSpacing(0)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.test_plan_btn = QtWidgets.QPushButton(self.centralwidget)
        self.test_plan_btn.setMinimumSize(QtCore.QSize(0, 50))
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(False)
        font.setWeight(50)
        self.test_plan_btn.setFont(font)
        self.test_plan_btn.setStyleSheet("")
        self.test_plan_btn.setObjectName("test_plan_btn")
        self.verticalLayout_5.addWidget(self.test_plan_btn)
        self.frame = QtWidgets.QFrame(self.centralwidget)
        self.frame.setMinimumSize(QtCore.QSize(150, 20))
        self.frame.setStyleSheet("border:none")
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout(self.frame)
        self.horizontalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_4.setSpacing(0)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.test_plan_label = QtWidgets.QLabel(self.frame)
        self.test_plan_label.setMinimumSize(QtCore.QSize(150, 30))
        self.test_plan_label.setStyleSheet("border:none")
        self.test_plan_label.setText("")
        self.test_plan_label.setAlignment(QtCore.Qt.AlignCenter)
        self.test_plan_label.setObjectName("test_plan_label")
        self.horizontalLayout_4.addWidget(self.test_plan_label)
        self.verticalLayout_5.addWidget(self.frame)
        self.horizontalLayout.addLayout(self.verticalLayout_5)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem1)
        self.verticalLayout_6 = QtWidgets.QVBoxLayout()
        self.verticalLayout_6.setSpacing(0)
        self.verticalLayout_6.setObjectName("verticalLayout_6")
        self.project_info_btn = QtWidgets.QPushButton(self.centralwidget)
        self.project_info_btn.setMinimumSize(QtCore.QSize(0, 50))
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(False)
        font.setWeight(50)
        self.project_info_btn.setFont(font)
        self.project_info_btn.setStyleSheet("")
        self.project_info_btn.setObjectName("project_info_btn")
        self.verticalLayout_6.addWidget(self.project_info_btn)
        self.frame_2 = QtWidgets.QFrame(self.centralwidget)
        self.frame_2.setMinimumSize(QtCore.QSize(150, 20))
        self.frame_2.setStyleSheet("border:none")
        self.frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_2.setObjectName("frame_2")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.frame_2)
        self.horizontalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_5.setSpacing(0)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.project_info_label = QtWidgets.QLabel(self.frame_2)
        self.project_info_label.setMinimumSize(QtCore.QSize(150, 30))
        self.project_info_label.setStyleSheet("border:none")
        self.project_info_label.setText("")
        self.project_info_label.setAlignment(QtCore.Qt.AlignCenter)
        self.project_info_label.setObjectName("project_info_label")
        self.horizontalLayout_5.addWidget(self.project_info_label)
        self.verticalLayout_6.addWidget(self.frame_2)
        self.horizontalLayout.addLayout(self.verticalLayout_6)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem2)
        self.verticalLayout_7 = QtWidgets.QVBoxLayout()
        self.verticalLayout_7.setSpacing(0)
        self.verticalLayout_7.setObjectName("verticalLayout_7")
        self.machine_peripherals_btn = QtWidgets.QPushButton(self.centralwidget)
        self.machine_peripherals_btn.setMinimumSize(QtCore.QSize(0, 50))
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(False)
        font.setWeight(50)
        self.machine_peripherals_btn.setFont(font)
        self.machine_peripherals_btn.setStyleSheet("")
        self.machine_peripherals_btn.setObjectName("machine_peripherals_btn")
        self.verticalLayout_7.addWidget(self.machine_peripherals_btn)
        self.frame_3 = QtWidgets.QFrame(self.centralwidget)
        self.frame_3.setMinimumSize(QtCore.QSize(150, 20))
        self.frame_3.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.frame_3.setStyleSheet("border:none")
        self.frame_3.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_3.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_3.setObjectName("frame_3")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.frame_3)
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_6.setSpacing(0)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.machine_peripherals_label = QtWidgets.QLabel(self.frame_3)
        self.machine_peripherals_label.setMinimumSize(QtCore.QSize(150, 30))
        self.machine_peripherals_label.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.machine_peripherals_label.setStyleSheet("border:none")
        self.machine_peripherals_label.setText("")
        self.machine_peripherals_label.setAlignment(QtCore.Qt.AlignCenter)
        self.machine_peripherals_label.setObjectName("machine_peripherals_label")
        self.horizontalLayout_6.addWidget(self.machine_peripherals_label)
        self.verticalLayout_7.addWidget(self.frame_3)
        self.horizontalLayout.addLayout(self.verticalLayout_7)
        spacerItem3 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem3)
        self.verticalLayout = QtWidgets.QVBoxLayout()
        self.verticalLayout.setContentsMargins(0, 0, -1, -1)
        self.verticalLayout.setSpacing(0)
        self.verticalLayout.setObjectName("verticalLayout")
        self.test_tools_btn = QtWidgets.QPushButton(self.centralwidget)
        self.test_tools_btn.setMinimumSize(QtCore.QSize(0, 50))
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(False)
        font.setWeight(50)
        self.test_tools_btn.setFont(font)
        self.test_tools_btn.setObjectName("test_tools_btn")
        self.verticalLayout.addWidget(self.test_tools_btn)
        self.frame_4 = QtWidgets.QFrame(self.centralwidget)
        self.frame_4.setMinimumSize(QtCore.QSize(150, 20))
        self.frame_4.setStyleSheet("border:none")
        self.frame_4.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_4.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_4.setObjectName("frame_4")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.frame_4)
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.test_tools_label = QtWidgets.QLabel(self.frame_4)
        self.test_tools_label.setMinimumSize(QtCore.QSize(150, 30))
        font = QtGui.QFont()
        font.setPointSize(9)
        font.setBold(False)
        font.setWeight(50)
        self.test_tools_label.setFont(font)
        self.test_tools_label.setStyleSheet("border:none")
        self.test_tools_label.setText("")
        self.test_tools_label.setAlignment(QtCore.Qt.AlignCenter)
        self.test_tools_label.setObjectName("test_tools_label")
        self.horizontalLayout_2.addWidget(self.test_tools_label)
        self.verticalLayout.addWidget(self.frame_4)
        self.horizontalLayout.addLayout(self.verticalLayout)
        spacerItem4 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem4)
        self.label = QtWidgets.QLabel(self.centralwidget)
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.label.setFont(font)
        self.label.setStyleSheet("")
        self.label.setObjectName("label")
        self.horizontalLayout.addWidget(self.label)
        self.comboBox_project = ExtendedComboBox(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.comboBox_project.sizePolicy().hasHeightForWidth())
        self.comboBox_project.setSizePolicy(sizePolicy)
        self.comboBox_project.setMinimumSize(QtCore.QSize(600, 45))
        self.comboBox_project.setMaximumSize(QtCore.QSize(600, 45))
        self.comboBox_project.setStyleSheet("")
        self.comboBox_project.setSizeAdjustPolicy(QtWidgets.QComboBox.AdjustToContents)
        self.comboBox_project.setObjectName("comboBox_project")
        self.horizontalLayout.addWidget(self.comboBox_project)
        self.refresh_project_btn = QtWidgets.QPushButton(self.centralwidget)
        self.refresh_project_btn.setMinimumSize(QtCore.QSize(0, 45))
        self.refresh_project_btn.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.refresh_project_btn.setFont(font)
        self.refresh_project_btn.setObjectName("refresh_project_btn")
        self.horizontalLayout.addWidget(self.refresh_project_btn)
        spacerItem5 = QtWidgets.QSpacerItem(35, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem5)
        self.verticalLayout_3.addLayout(self.horizontalLayout)
        self.label_2 = QtWidgets.QLabel(self.centralwidget)
        self.label_2.setMinimumSize(QtCore.QSize(0, 30))
        self.label_2.setText("")
        self.label_2.setObjectName("label_2")
        self.verticalLayout_3.addWidget(self.label_2)
        self.stackedWidget = QtWidgets.QStackedWidget(self.centralwidget)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.stackedWidget.setFont(font)
        self.stackedWidget.setObjectName("stackedWidget")
        self.verticalLayout_3.addWidget(self.stackedWidget)
        self.verticalLayout_4.addLayout(self.verticalLayout_3)
        MainWindow.setCentralWidget(self.centralwidget)
        self.actionSystemSetting = QtWidgets.QAction(MainWindow)
        self.actionSystemSetting.setObjectName("actionSystemSetting")
        self.actionNewProject = QtWidgets.QAction(MainWindow)
        self.actionNewProject.setVisible(True)
        self.actionNewProject.setIconVisibleInMenu(True)
        self.actionNewProject.setObjectName("actionNewProject")
        self.actionEditProject = QtWidgets.QAction(MainWindow)
        self.actionEditProject.setObjectName("actionEditProject")
        self.actionOpenProject = QtWidgets.QAction(MainWindow)
        self.actionOpenProject.setObjectName("actionOpenProject")
        self.actionRelayPower = QtWidgets.QAction(MainWindow)
        self.actionRelayPower.setObjectName("actionRelayPower")
        self.actionhelp_about = QtWidgets.QAction(MainWindow)
        self.actionhelp_about.setObjectName("actionhelp_about")
        self.actionProgramPower = QtWidgets.QAction(MainWindow)
        self.actionProgramPower.setObjectName("actionProgramPower")
        self.actionMatePushImageTool = QtWidgets.QAction(MainWindow)
        self.actionMatePushImageTool.setObjectName("actionMatePushImageTool")
        self.actionPictureTool = QtWidgets.QAction(MainWindow)
        self.actionPictureTool.setObjectName("actionPictureTool")
        self.actionAutoPress = QtWidgets.QAction(MainWindow)
        self.actionAutoPress.setObjectName("actionAutoPress")
        self.actionIoControl = QtWidgets.QAction(MainWindow)
        self.actionIoControl.setObjectName("actionIoControl")
        self.actionProjectSetting = QtWidgets.QAction(MainWindow)
        self.actionProjectSetting.setObjectName("actionProjectSetting")
        self.actionMachineInit = QtWidgets.QAction(MainWindow)
        self.actionMachineInit.setObjectName("actionMachineInit")
        self.actionMachineStart = QtWidgets.QAction(MainWindow)
        self.actionMachineStart.setObjectName("actionMachineStart")
        self.actionMachineReset = QtWidgets.QAction(MainWindow)
        self.actionMachineReset.setObjectName("actionMachineReset")
        self.actionMachineStop = QtWidgets.QAction(MainWindow)
        self.actionMachineStop.setObjectName("actionMachineStop")
        self.actionPositionControl = QtWidgets.QAction(MainWindow)
        self.actionPositionControl.setObjectName("actionPositionControl")
        self.actionProgramPowerIT_M3200 = QtWidgets.QAction(MainWindow)
        self.actionProgramPowerIT_M3200.setObjectName("actionProgramPowerIT_M3200")
        self.action_logic = QtWidgets.QAction(MainWindow)
        self.action_logic.setObjectName("action_logic")
        self.DBCMessageSend = QtWidgets.QAction(MainWindow)
        self.DBCMessageSend.setObjectName("DBCMessageSend")
        self.DBCMessageRecv = QtWidgets.QAction(MainWindow)
        self.DBCMessageRecv.setObjectName("DBCMessageRecv")
        self.actionTriColorLight = QtWidgets.QAction(MainWindow)
        self.actionTriColorLight.setObjectName("actionTriColorLight")
        self.actionExportProject = QtWidgets.QAction(MainWindow)
        self.actionExportProject.setObjectName("actionExportProject")
        self.actionPullPlan = QtWidgets.QAction(MainWindow)
        self.actionPullPlan.setObjectName("actionPullPlan")

        self.retranslateUi(MainWindow)
        QtCore.QMetaObject.connectSlotsByName(MainWindow)

    def retranslateUi(self, MainWindow):
        _translate = QtCore.QCoreApplication.translate
        MainWindow.setWindowTitle(_translate("MainWindow", "产品自动化测试平台"))
        self.test_plan_btn.setText(_translate("MainWindow", "测试计划"))
        self.project_info_btn.setText(_translate("MainWindow", "项目信息"))
        self.machine_peripherals_btn.setText(_translate("MainWindow", "机台外设"))
        self.test_tools_btn.setText(_translate("MainWindow", "测试工具"))
        self.label.setText(_translate("MainWindow", "项目名称："))
        self.refresh_project_btn.setText(_translate("MainWindow", "刷新"))
        self.actionSystemSetting.setText(_translate("MainWindow", "系统设置"))
        self.actionNewProject.setText(_translate("MainWindow", "新建项目"))
        self.actionEditProject.setText(_translate("MainWindow", "编辑项目"))
        self.actionOpenProject.setText(_translate("MainWindow", "打开项目"))
        self.actionRelayPower.setText(_translate("MainWindow", "继电器"))
        self.actionhelp_about.setText(_translate("MainWindow", "帮助"))
        self.actionProgramPower.setText(_translate("MainWindow", "Tommens程控电源"))
        self.actionMatePushImageTool.setText(_translate("MainWindow", "Mate推图工具"))
        self.actionPictureTool.setText(_translate("MainWindow", "图片生成工具"))
        self.actionAutoPress.setText(_translate("MainWindow", "自动按键"))
        self.actionIoControl.setText(_translate("MainWindow", "板卡运动控制工具"))
        self.actionProjectSetting.setText(_translate("MainWindow", "项目设置"))
        self.actionMachineInit.setText(_translate("MainWindow", "机台初始化"))
        self.actionMachineStart.setText(_translate("MainWindow", "机台开始"))
        self.actionMachineReset.setText(_translate("MainWindow", "机台复位"))
        self.actionMachineStop.setText(_translate("MainWindow", "机台停止"))
        self.actionPositionControl.setText(_translate("MainWindow", "板卡位置控制工具"))
        self.actionProgramPowerIT_M3200.setText(_translate("MainWindow", "IT-M3200程控电源"))
        self.action_logic.setText(_translate("MainWindow", "逻辑分析仪"))
        self.DBCMessageSend.setText(_translate("MainWindow", "DBC发送工具"))
        self.DBCMessageRecv.setText(_translate("MainWindow", "DBC接收工具"))
        self.actionTriColorLight.setText(_translate("MainWindow", "三色灯"))
        self.actionExportProject.setText(_translate("MainWindow", "导出项目"))
        self.actionPullPlan.setText(_translate("MainWindow", "拉取测试计划"))
from view.ExtendedComboBox import ExtendedComboBox
