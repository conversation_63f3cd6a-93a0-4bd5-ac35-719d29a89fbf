#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的视频压缩测试脚本
直接测试指定文件的压缩功能
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.CompressManager import CompressManager


def main():
    """主函数 - 直接测试视频压缩"""
    
    # ========== 配置区域 ==========
    # 请修改这里的文件路径为你要测试的视频文件
    TEST_VIDEO_FILE = r"C:\Users\<USER>\Videos\Captures\simple_compress_test.py - HWTreeATE - Visual Studio Code 2025-07-17 15-34-19.mp4"  # 请替换为你的实际文件路径
    # =============================
    
    print("🎬 视频压缩测试脚本")
    print("-" * 50)
    
    # 检查文件是否存在
    if not os.path.exists(TEST_VIDEO_FILE):
        print(f"❌ 错误: 测试文件不存在")
        print(f"   文件路径: {TEST_VIDEO_FILE}")
        print(f"   请修改脚本中的 TEST_VIDEO_FILE 变量为正确的文件路径")
        return
    
    # 显示文件信息
    file_size = os.path.getsize(TEST_VIDEO_FILE)
    print(f"📁 输入文件: {TEST_VIDEO_FILE}")
    print(f"📊 文件大小: {file_size:,} bytes ({file_size / 1024 / 1024:.2f} MB)")
    
    try:
        # 创建压缩管理器实例（使用默认 ffmpeg）
        print(f"\n🔧 初始化压缩管理器...")
        compressor = CompressManager()
        print(f"✅ FFmpeg 路径: {compressor.ffmpeg_executable}")
        
        # 开始压缩
        print(f"\n🚀 开始压缩...")
        start_time = time.time()
        
        # 调用压缩方法
        output_file = compressor.compress_video(TEST_VIDEO_FILE)
        
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        # 检查结果
        if output_file and os.path.exists(output_file):
            output_size = os.path.getsize(output_file)
            compression_ratio = (1 - output_size / file_size) * 100
            
            print(f"\n✅ 压缩成功!")
            print(f"📁 输出文件: {output_file}")
            print(f"📊 压缩后大小: {output_size:,} bytes ({output_size / 1024 / 1024:.2f} MB)")
            print(f"📈 压缩率: {compression_ratio:.1f}%")
            print(f"⏱️  压缩耗时: {elapsed_time:.2f} 秒")
            
            # 计算压缩效果
            if compression_ratio > 0:
                print(f"💾 节省空间: {file_size - output_size:,} bytes")
            else:
                print(f"⚠️  文件变大了: {output_size - file_size:,} bytes")
                
        else:
            print(f"❌ 压缩失败!")
            print(f"   请检查日志获取详细错误信息")
            
    except FileNotFoundError as e:
        print(f"❌ FFmpeg 未找到: {e}")
        print(f"   请确保 FFmpeg 已正确安装")
        print(f"   或者检查项目目录下的 ffmpeg/bin/ffmpeg.exe 是否存在")
        
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        print(f"   请检查文件路径和 FFmpeg 配置")


if __name__ == "__main__":
    main()
